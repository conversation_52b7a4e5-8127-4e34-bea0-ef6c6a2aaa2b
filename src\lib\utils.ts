import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Utility function to merge Tailwind CSS classes
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format currency in Kenya Shillings
 */
export function formatKSh(amount: number): string {
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

/**
 * Format date for Kenyan legal system
 */
export function formatLegalDate(date: Date): string {
  return new Intl.DateTimeFormat('en-KE', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  }).format(date)
}

/**
 * Format time for court proceedings
 */
export function formatCourtTime(date: Date): string {
  return new Intl.DateTimeFormat('en-KE', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  }).format(date)
}

/**
 * Generate case number format
 */
export function generateCaseNumber(
  type: 'CRIMINAL' | 'CIVIL' | 'FAMILY' | 'COMMERCIAL',
  year: number,
  sequence: number,
  court: string = 'MC'
): string {
  const typeCode = {
    CRIMINAL: 'CR',
    CIVIL: 'CV',
    FAMILY: 'FM',
    COMMERCIAL: 'CM'
  }[type]
  
  return `${typeCode} ${sequence}/${year} ${court}`
}

/**
 * Validate Kenya ID number
 */
export function validateKenyanID(id: string): boolean {
  const idRegex = /^\d{8}$/
  return idRegex.test(id)
}

/**
 * Validate Kenya phone number
 */
export function validateKenyanPhone(phone: string): boolean {
  const phoneRegex = /^(\+254|0)[17]\d{8}$/
  return phoneRegex.test(phone)
}

/**
 * Get case status color for UI
 */
export function getCaseStatusColor(status: string): string {
  const statusColors = {
    'PENDING': 'status-pending',
    'ACTIVE': 'status-active',
    'HEARING': 'status-active',
    'JUDGMENT': 'status-active',
    'COMPLETED': 'status-completed',
    'DISMISSED': 'status-cancelled',
    'WITHDRAWN': 'status-cancelled',
  }
  
  return statusColors[status as keyof typeof statusColors] || 'status-pending'
}

/**
 * Calculate bail amount based on offense type (simplified)
 */
export function calculateBailAmount(offenseType: string, offenseGravity: 'LOW' | 'MEDIUM' | 'HIGH'): number {
  const baseAmounts = {
    'THEFT': { LOW: 10000, MEDIUM: 25000, HIGH: 50000 },
    'ASSAULT': { LOW: 15000, MEDIUM: 30000, HIGH: 75000 },
    'FRAUD': { LOW: 50000, MEDIUM: 100000, HIGH: 200000 },
    'DRUG_OFFENSE': { LOW: 20000, MEDIUM: 50000, HIGH: 100000 },
    'TRAFFIC': { LOW: 5000, MEDIUM: 15000, HIGH: 30000 },
    'DEFAULT': { LOW: 10000, MEDIUM: 25000, HIGH: 50000 }
  }
  
  const amounts = baseAmounts[offenseType as keyof typeof baseAmounts] || baseAmounts.DEFAULT
  return amounts[offenseGravity]
}

/**
 * Get user-friendly error messages
 */
export function getUserFriendlyError(error: string): string {
  const errorMessages = {
    'NETWORK_ERROR': 'Please check your internet connection and try again.',
    'VALIDATION_ERROR': 'Please check the information you entered and try again.',
    'PERMISSION_ERROR': 'You do not have permission to perform this action.',
    'NOT_FOUND': 'The requested information could not be found.',
    'SERVER_ERROR': 'There was a problem with the system. Please try again later.',
    'TIMEOUT': 'The request took too long. Please try again.',
  }
  
  return errorMessages[error as keyof typeof errorMessages] || 'An unexpected error occurred. Please try again.'
}

/**
 * Debounce function for search inputs
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Simple text search function
 */
export function simpleSearch(items: any[], searchTerm: string, searchFields: string[]): any[] {
  if (!searchTerm.trim()) return items
  
  const term = searchTerm.toLowerCase()
  
  return items.filter(item => 
    searchFields.some(field => {
      const value = getNestedValue(item, field)
      return value && value.toString().toLowerCase().includes(term)
    })
  )
}

/**
 * Get nested object value by path
 */
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Check if user is on mobile device
 */
export function isMobile(): boolean {
  if (typeof window === 'undefined') return false
  return window.innerWidth < 768
}

/**
 * Scroll to element smoothly
 */
export function scrollToElement(elementId: string): void {
  const element = document.getElementById(elementId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}
