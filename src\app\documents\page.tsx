'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Card, CardHeader, CardContent, QuickActionCard, InfoCard } from '@/components/ui/Card'
import { FormField, Label, Select, Input } from '@/components/ui/Form'
import { formatLegalDate } from '@/lib/utils'
import { useAuth, withAuth } from '@/contexts/AuthContext'

interface DocumentTemplate {
  id: string
  name: string
  description: string
  category: 'judgment' | 'order' | 'notice' | 'bail' | 'sentence'
  content: string
  fields: Array<{
    name: string
    label: string
    type: 'text' | 'date' | 'number' | 'select'
    options?: string[]
    required: boolean
  }>
}

interface Document {
  id: string
  title: string
  templateId: string
  content: string
  status: 'draft' | 'review' | 'approved' | 'published'
  createdAt: string
  updatedAt: string
  caseNumber?: string
}

function DocumentsPage() {
  const { user, hasPermission } = useAuth()
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null)
  const [documents, setDocuments] = useState<Document[]>([
    {
      id: '1',
      title: 'Judgment - CR 45/2024 MC',
      templateId: 'criminal-judgment',
      content: 'Draft judgment for Republic vs John Doe...',
      status: 'draft',
      createdAt: '2024-07-10T09:00:00Z',
      updatedAt: '2024-07-10T10:30:00Z',
      caseNumber: 'CR 45/2024 MC'
    },
    {
      id: '2',
      title: 'Bail Order - CR 67/2024 MC',
      templateId: 'bail-order',
      content: 'Bail order for Republic vs Mary Johnson...',
      status: 'approved',
      createdAt: '2024-07-09T14:00:00Z',
      updatedAt: '2024-07-09T15:00:00Z',
      caseNumber: 'CR 67/2024 MC'
    }
  ])
  const [showTemplateSelector, setShowTemplateSelector] = useState(false)
  const [editingDocument, setEditingDocument] = useState<Document | null>(null)

  // Legal document templates
  const templates: DocumentTemplate[] = [
    {
      id: 'criminal-judgment',
      name: 'Criminal Case Judgment',
      description: 'Standard template for criminal case judgments',
      category: 'judgment',
      content: `REPUBLIC OF KENYA
IN THE MAGISTRATE'S COURT AT [COURT_LOCATION]

CRIMINAL CASE NO. [CASE_NUMBER]

REPUBLIC
VS
[ACCUSED_NAME]

JUDGMENT

Before: [MAGISTRATE_NAME], Senior Resident Magistrate

Date: [JUDGMENT_DATE]

CHARGES:
[CHARGES_DETAILS]

FACTS:
[CASE_FACTS]

ANALYSIS:
[LEGAL_ANALYSIS]

FINDING:
[COURT_FINDING]

SENTENCE:
[SENTENCE_DETAILS]

Delivered in open court this [JUDGMENT_DATE]

[MAGISTRATE_NAME]
Senior Resident Magistrate`,
      fields: [
        { name: 'COURT_LOCATION', label: 'Court Location', type: 'text', required: true },
        { name: 'CASE_NUMBER', label: 'Case Number', type: 'text', required: true },
        { name: 'ACCUSED_NAME', label: 'Accused Name', type: 'text', required: true },
        { name: 'MAGISTRATE_NAME', label: 'Magistrate Name', type: 'text', required: true },
        { name: 'JUDGMENT_DATE', label: 'Judgment Date', type: 'date', required: true },
        { name: 'CHARGES_DETAILS', label: 'Charges Details', type: 'text', required: true },
        { name: 'CASE_FACTS', label: 'Case Facts', type: 'text', required: true },
        { name: 'LEGAL_ANALYSIS', label: 'Legal Analysis', type: 'text', required: true },
        { name: 'COURT_FINDING', label: 'Court Finding', type: 'select', options: ['Guilty', 'Not Guilty'], required: true },
        { name: 'SENTENCE_DETAILS', label: 'Sentence Details', type: 'text', required: false }
      ]
    },
    {
      id: 'bail-order',
      name: 'Bail Order',
      description: 'Template for bail application orders',
      category: 'order',
      content: `REPUBLIC OF KENYA
IN THE MAGISTRATE'S COURT AT [COURT_LOCATION]

CRIMINAL CASE NO. [CASE_NUMBER]

REPUBLIC
VS
[ACCUSED_NAME]

BAIL ORDER

Before: [MAGISTRATE_NAME], Senior Resident Magistrate

Date: [ORDER_DATE]

UPON hearing the application for bail by the accused person [ACCUSED_NAME] charged with [CHARGES];

AND UPON considering the submissions by the prosecution and defense;

IT IS HEREBY ORDERED THAT:

1. The accused person [ACCUSED_NAME] is hereby admitted to bail in the sum of KSh [BAIL_AMOUNT];

2. The accused shall deposit the said sum with the court or provide [SURETY_TYPE];

3. The accused shall report to [REPORTING_STATION] every [REPORTING_FREQUENCY];

4. The accused shall not interfere with witnesses;

5. The accused shall surrender all travel documents to the court;

6. [ADDITIONAL_CONDITIONS]

7. The matter is mentioned on [MENTION_DATE] for [MENTION_PURPOSE].

Dated this [ORDER_DATE]

[MAGISTRATE_NAME]
Senior Resident Magistrate`,
      fields: [
        { name: 'COURT_LOCATION', label: 'Court Location', type: 'text', required: true },
        { name: 'CASE_NUMBER', label: 'Case Number', type: 'text', required: true },
        { name: 'ACCUSED_NAME', label: 'Accused Name', type: 'text', required: true },
        { name: 'MAGISTRATE_NAME', label: 'Magistrate Name', type: 'text', required: true },
        { name: 'ORDER_DATE', label: 'Order Date', type: 'date', required: true },
        { name: 'CHARGES', label: 'Charges', type: 'text', required: true },
        { name: 'BAIL_AMOUNT', label: 'Bail Amount (KSh)', type: 'number', required: true },
        { name: 'SURETY_TYPE', label: 'Surety Type', type: 'select', options: ['Cash Bail', 'Bond with Surety', 'Personal Bond'], required: true },
        { name: 'REPORTING_STATION', label: 'Reporting Station', type: 'text', required: true },
        { name: 'REPORTING_FREQUENCY', label: 'Reporting Frequency', type: 'select', options: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Weekly', 'Bi-weekly'], required: true },
        { name: 'ADDITIONAL_CONDITIONS', label: 'Additional Conditions', type: 'text', required: false },
        { name: 'MENTION_DATE', label: 'Next Mention Date', type: 'date', required: true },
        { name: 'MENTION_PURPOSE', label: 'Mention Purpose', type: 'text', required: true }
      ]
    },
    {
      id: 'civil-judgment',
      name: 'Civil Case Judgment',
      description: 'Standard template for civil case judgments',
      category: 'judgment',
      content: `REPUBLIC OF KENYA
IN THE MAGISTRATE'S COURT AT [COURT_LOCATION]

CIVIL CASE NO. [CASE_NUMBER]

[PLAINTIFF_NAME]
VS
[DEFENDANT_NAME]

JUDGMENT

Before: [MAGISTRATE_NAME], Senior Resident Magistrate

Date: [JUDGMENT_DATE]

PARTIES:
Plaintiff: [PLAINTIFF_NAME]
Defendant: [DEFENDANT_NAME]

CLAIM:
[CLAIM_DETAILS]

DEFENSE:
[DEFENSE_DETAILS]

ISSUES FOR DETERMINATION:
[ISSUES]

ANALYSIS AND FINDINGS:
[ANALYSIS]

ORDERS:
[COURT_ORDERS]

COSTS:
[COSTS_ORDER]

Delivered in open court this [JUDGMENT_DATE]

[MAGISTRATE_NAME]
Senior Resident Magistrate`,
      fields: [
        { name: 'COURT_LOCATION', label: 'Court Location', type: 'text', required: true },
        { name: 'CASE_NUMBER', label: 'Case Number', type: 'text', required: true },
        { name: 'PLAINTIFF_NAME', label: 'Plaintiff Name', type: 'text', required: true },
        { name: 'DEFENDANT_NAME', label: 'Defendant Name', type: 'text', required: true },
        { name: 'MAGISTRATE_NAME', label: 'Magistrate Name', type: 'text', required: true },
        { name: 'JUDGMENT_DATE', label: 'Judgment Date', type: 'date', required: true },
        { name: 'CLAIM_DETAILS', label: 'Claim Details', type: 'text', required: true },
        { name: 'DEFENSE_DETAILS', label: 'Defense Details', type: 'text', required: true },
        { name: 'ISSUES', label: 'Issues for Determination', type: 'text', required: true },
        { name: 'ANALYSIS', label: 'Analysis and Findings', type: 'text', required: true },
        { name: 'COURT_ORDERS', label: 'Court Orders', type: 'text', required: true },
        { name: 'COSTS_ORDER', label: 'Costs Order', type: 'text', required: true }
      ]
    }
  ]

  const createNewDocument = (template: DocumentTemplate) => {
    const newDoc: Document = {
      id: Date.now().toString(),
      title: `New ${template.name}`,
      templateId: template.id,
      content: template.content,
      status: 'draft',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    setDocuments(prev => [newDoc, ...prev])
    setEditingDocument(newDoc)
    setShowTemplateSelector(false)
  }

  const getStatusColor = (status: Document['status']) => {
    switch (status) {
      case 'draft': return 'status-pending'
      case 'review': return 'status-active'
      case 'approved': return 'status-completed'
      case 'published': return 'status-completed'
      default: return 'status-pending'
    }
  }

  const getCategoryIcon = (category: DocumentTemplate['category']) => {
    switch (category) {
      case 'judgment':
        return (
          <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        )
      case 'order':
        return (
          <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
          </svg>
        )
      case 'notice':
        return (
          <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2zM4 7h8V5H4v2z" />
          </svg>
        )
      case 'bail':
        return (
          <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        )
      case 'sentence':
        return (
          <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button 
                variant="secondary" 
                onClick={() => window.history.back()}
                className="flex items-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Back to Dashboard
              </Button>
              <h1 className="text-2xl font-bold text-gray-900">Document Management</h1>
            </div>
            <div className="flex items-center space-x-4">
              {hasPermission('draft_documents') && (
                <Button 
                  variant="primary" 
                  onClick={() => setShowTemplateSelector(true)}
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  New Document
                </Button>
              )}
              <div className="text-right">
                <p className="text-sm text-gray-600">
                  {user?.displayName}
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {showTemplateSelector ? (
          /* Template Selection */
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Choose a Template</h2>
              <Button 
                variant="secondary" 
                onClick={() => setShowTemplateSelector(false)}
              >
                Cancel
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {templates.map(template => (
                <QuickActionCard
                  key={template.id}
                  title={template.name}
                  description={template.description}
                  icon={getCategoryIcon(template.category)}
                  onClick={() => createNewDocument(template)}
                />
              ))}
            </div>
            <div className="mt-6">
              <InfoCard type="info" title="Template Guidelines">
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Choose the appropriate template for your document type</li>
                  <li>All required fields must be filled before the document can be finalized</li>
                  <li>Templates follow standard Kenyan legal formatting</li>
                  <li>You can customize the content after selecting a template</li>
                </ul>
              </InfoCard>
            </div>
          </div>
        ) : editingDocument ? (
          /* Document Editor */
          <DocumentEditor 
            document={editingDocument}
            template={templates.find(t => t.id === editingDocument.templateId)}
            onSave={(updatedDoc) => {
              setDocuments(prev => prev.map(doc => 
                doc.id === updatedDoc.id ? updatedDoc : doc
              ))
              setEditingDocument(null)
            }}
            onCancel={() => setEditingDocument(null)}
          />
        ) : (
          /* Document List */
          <div>
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">My Documents</h2>
              <div className="grid grid-cols-1 gap-4">
                {documents.map(doc => (
                  <Card key={doc.id} className="hover:shadow-lg transition-shadow duration-200">
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">
                            {doc.title}
                          </h3>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                            <div>
                              <span className="font-medium">Status:</span>
                              <span className={`ml-2 status-badge ${getStatusColor(doc.status)}`}>
                                {doc.status.toUpperCase()}
                              </span>
                            </div>
                            <div>
                              <span className="font-medium">Created:</span>
                              <span className="ml-2">{formatLegalDate(new Date(doc.createdAt))}</span>
                            </div>
                            <div>
                              <span className="font-medium">Updated:</span>
                              <span className="ml-2">{formatLegalDate(new Date(doc.updatedAt))}</span>
                            </div>
                            {doc.caseNumber && (
                              <div>
                                <span className="font-medium">Case:</span>
                                <span className="ml-2">{doc.caseNumber}</span>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 ml-4">
                          <Button 
                            variant="primary" 
                            size="small"
                            onClick={() => setEditingDocument(doc)}
                          >
                            Edit
                          </Button>
                          <Button 
                            variant="secondary" 
                            size="small"
                            onClick={() => alert('Preview feature coming soon!')}
                          >
                            Preview
                          </Button>
                          {hasPermission('approve_documents') && doc.status === 'review' && (
                            <Button 
                              variant="success" 
                              size="small"
                              onClick={() => {
                                setDocuments(prev => prev.map(d => 
                                  d.id === doc.id ? { ...d, status: 'approved' } : d
                                ))
                              }}
                            >
                              Approve
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <InfoCard type="info" title="Total Documents">
                <p className="text-2xl font-bold">{documents.length}</p>
              </InfoCard>
              <InfoCard type="warning" title="Drafts">
                <p className="text-2xl font-bold">{documents.filter(d => d.status === 'draft').length}</p>
              </InfoCard>
              <InfoCard type="info" title="Under Review">
                <p className="text-2xl font-bold">{documents.filter(d => d.status === 'review').length}</p>
              </InfoCard>
              <InfoCard type="success" title="Approved">
                <p className="text-2xl font-bold">{documents.filter(d => d.status === 'approved').length}</p>
              </InfoCard>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}

// Simple Document Editor Component
function DocumentEditor({ 
  document, 
  template, 
  onSave, 
  onCancel 
}: {
  document: Document
  template?: DocumentTemplate
  onSave: (doc: Document) => void
  onCancel: () => void
}) {
  const [content, setContent] = useState(document.content)
  const [title, setTitle] = useState(document.title)
  const [fieldValues, setFieldValues] = useState<Record<string, string>>({})

  const handleSave = () => {
    let updatedContent = content
    
    // Replace template fields with values
    if (template) {
      template.fields.forEach(field => {
        const value = fieldValues[field.name] || `[${field.name}]`
        updatedContent = updatedContent.replace(new RegExp(`\\[${field.name}\\]`, 'g'), value)
      })
    }

    const updatedDoc: Document = {
      ...document,
      title,
      content: updatedContent,
      updatedAt: new Date().toISOString()
    }
    onSave(updatedDoc)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Edit Document</h2>
        <div className="flex space-x-2">
          <Button variant="secondary" onClick={onCancel}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleSave}>
            Save Document
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Template Fields */}
        {template && (
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">Document Fields</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <FormField>
                    <Label htmlFor="title">Document Title</Label>
                    <Input
                      id="title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      placeholder="Enter document title"
                    />
                  </FormField>
                  
                  {template.fields.map(field => (
                    <FormField key={field.name}>
                      <Label htmlFor={field.name} required={field.required}>
                        {field.label}
                      </Label>
                      {field.type === 'select' ? (
                        <Select
                          id={field.name}
                          value={fieldValues[field.name] || ''}
                          onChange={(e) => setFieldValues(prev => ({
                            ...prev,
                            [field.name]: e.target.value
                          }))}
                          options={field.options?.map(opt => ({ value: opt, label: opt })) || []}
                          placeholder={`Select ${field.label.toLowerCase()}`}
                        />
                      ) : (
                        <Input
                          id={field.name}
                          type={field.type}
                          value={fieldValues[field.name] || ''}
                          onChange={(e) => setFieldValues(prev => ({
                            ...prev,
                            [field.name]: e.target.value
                          }))}
                          placeholder={`Enter ${field.label.toLowerCase()}`}
                        />
                      )}
                    </FormField>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Document Content */}
        <div className={template ? 'lg:col-span-2' : 'lg:col-span-3'}>
          <Card className="h-[600px] flex flex-col">
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900">Document Content</h3>
            </CardHeader>
            <CardContent className="flex-1">
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className="w-full h-full p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter document content..."
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

// Export with authentication protection
export default withAuth(DocumentsPage, ['draft_documents'])
