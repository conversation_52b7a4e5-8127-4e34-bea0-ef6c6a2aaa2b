/**
 * Advanced Judgment Drafting System for KesiTrack
 * AI-assisted judgment writing with legal reasoning and precedent integration
 */

import { KenyaLawReportsEngine, type KenyaLawCase } from './kenya-law-reports'
import { CaseAnalysisEngine, type CaseAnalysis } from './ai-analysis'

export interface JudgmentDraft {
  id: string
  caseNumber: string
  title: string
  judgmentType: 'criminal' | 'civil' | 'family' | 'commercial'
  sections: JudgmentSection[]
  similarCases: KenyaLawCase[]
  legalReferences: LegalReference[]
  createdAt: string
  updatedAt: string
  status: 'draft' | 'review' | 'final'
  confidence: number
}

export interface JudgmentSection {
  id: string
  type: 'header' | 'parties' | 'background' | 'issues' | 'analysis' | 'finding' | 'orders' | 'costs'
  title: string
  content: string
  suggestions: string[]
  precedents: string[]
  isRequired: boolean
  isComplete: boolean
}

export interface LegalReference {
  type: 'statute' | 'case_law' | 'constitution' | 'regulation'
  citation: string
  relevantText: string
  application: string
}

export interface JudgmentTemplate {
  id: string
  name: string
  type: 'criminal' | 'civil' | 'family' | 'commercial'
  sections: Omit<JudgmentSection, 'id' | 'content' | 'isComplete'>[]
  description: string
}

/**
 * Judgment Templates for different case types
 */
export const JUDGMENT_TEMPLATES: JudgmentTemplate[] = [
  {
    id: 'criminal-judgment',
    name: 'Criminal Case Judgment',
    type: 'criminal',
    description: 'Standard template for criminal case judgments',
    sections: [
      {
        type: 'header',
        title: 'Case Header',
        suggestions: ['Include court name, case number, parties, and date'],
        precedents: [],
        isRequired: true
      },
      {
        type: 'parties',
        title: 'Parties and Representation',
        suggestions: ['List prosecution and defense representation', 'Note if accused is self-represented'],
        precedents: [],
        isRequired: true
      },
      {
        type: 'background',
        title: 'Background and Charges',
        suggestions: ['State the charges clearly', 'Summarize the prosecution case', 'Note the plea entered'],
        precedents: [],
        isRequired: true
      },
      {
        type: 'issues',
        title: 'Issues for Determination',
        suggestions: ['List the key legal and factual issues', 'Reference burden of proof'],
        precedents: [],
        isRequired: true
      },
      {
        type: 'analysis',
        title: 'Analysis and Evidence',
        suggestions: ['Evaluate prosecution evidence', 'Consider defense arguments', 'Apply legal principles'],
        precedents: ['Standard of proof beyond reasonable doubt', 'Credibility of witnesses'],
        isRequired: true
      },
      {
        type: 'finding',
        title: 'Finding and Verdict',
        suggestions: ['State the court\'s finding clearly', 'Provide reasoning for the verdict'],
        precedents: [],
        isRequired: true
      },
      {
        type: 'orders',
        title: 'Orders and Sentence',
        suggestions: ['State the sentence if guilty', 'Consider mitigating and aggravating factors'],
        precedents: ['Sentencing guidelines', 'Mitigation principles'],
        isRequired: true
      }
    ]
  },
  {
    id: 'civil-judgment',
    name: 'Civil Case Judgment',
    type: 'civil',
    description: 'Standard template for civil case judgments',
    sections: [
      {
        type: 'header',
        title: 'Case Header',
        suggestions: ['Include court name, case number, parties, and date'],
        precedents: [],
        isRequired: true
      },
      {
        type: 'parties',
        title: 'Parties and Representation',
        suggestions: ['List plaintiff and defendant representation'],
        precedents: [],
        isRequired: true
      },
      {
        type: 'background',
        title: 'Background and Pleadings',
        suggestions: ['Summarize the claim', 'State the defense', 'Note any counterclaims'],
        precedents: [],
        isRequired: true
      },
      {
        type: 'issues',
        title: 'Issues for Determination',
        suggestions: ['List the disputed issues', 'Reference burden of proof'],
        precedents: [],
        isRequired: true
      },
      {
        type: 'analysis',
        title: 'Analysis and Findings',
        suggestions: ['Evaluate evidence', 'Apply relevant law', 'Make factual findings'],
        precedents: ['Balance of probabilities', 'Contract interpretation principles'],
        isRequired: true
      },
      {
        type: 'finding',
        title: 'Judgment',
        suggestions: ['State the court\'s decision', 'Provide clear reasoning'],
        precedents: [],
        isRequired: true
      },
      {
        type: 'orders',
        title: 'Orders',
        suggestions: ['Specify relief granted', 'Include any injunctions or declarations'],
        precedents: [],
        isRequired: true
      },
      {
        type: 'costs',
        title: 'Costs',
        suggestions: ['Determine costs liability', 'Consider any special circumstances'],
        precedents: ['Costs follow the event principle'],
        isRequired: false
      }
    ]
  }
]

/**
 * Legal Reasoning Templates
 */
export const LEGAL_REASONING_TEMPLATES = {
  CRIMINAL: {
    BURDEN_OF_PROOF: 'The prosecution bears the burden of proving the case against the accused beyond reasonable doubt. This is a high standard that requires the court to be sure of the accused\'s guilt.',
    CREDIBILITY_ASSESSMENT: 'In assessing the credibility of witnesses, the court considers their demeanor, consistency of testimony, and any corroborating evidence.',
    CIRCUMSTANTIAL_EVIDENCE: 'Where the case relies on circumstantial evidence, the circumstances must be consistent only with the guilt of the accused and inconsistent with any other rational conclusion.',
    MITIGATION: 'In considering mitigation, the court takes into account the personal circumstances of the accused, any remorse shown, and the likelihood of rehabilitation.'
  },
  CIVIL: {
    BALANCE_OF_PROBABILITIES: 'In civil cases, the standard of proof is on a balance of probabilities, meaning it is more likely than not that the facts alleged are true.',
    CONTRACT_INTERPRETATION: 'Contracts must be interpreted to give effect to the intention of the parties as expressed in the document, read in light of the surrounding circumstances.',
    DAMAGES_ASSESSMENT: 'Damages should place the injured party in the position they would have been in had the contract been properly performed.',
    NEGLIGENCE_STANDARD: 'The standard of care required is that of a reasonable person in the defendant\'s position, taking into account all relevant circumstances.'
  }
}

/**
 * Advanced Judgment Drafting Engine
 */
export class JudgmentDraftingEngine {
  /**
   * Create a new judgment draft based on case information
   */
  static async createJudgmentDraft(caseData: {
    caseNumber: string
    caseType: 'criminal' | 'civil' | 'family' | 'commercial'
    parties: { plaintiff?: string; defendant?: string; accused?: string }
    facts: string
    charges?: string[]
    issues: string[]
    evidence: string[]
  }): Promise<JudgmentDraft> {
    
    // Get appropriate template
    const template = JUDGMENT_TEMPLATES.find(t => t.type === caseData.caseType)
    if (!template) {
      throw new Error(`No template found for case type: ${caseData.caseType}`)
    }

    // Find similar cases
    const similarCases = KenyaLawReportsEngine.findSimilarCases({
      charges: caseData.charges,
      facts: caseData.facts,
      caseType: caseData.caseType,
      legalIssues: caseData.issues
    })

    // Generate sections with AI assistance
    const sections = await Promise.all(
      template.sections.map(async (sectionTemplate, index) => {
        const content = await this.generateSectionContent(sectionTemplate, caseData, similarCases)
        
        return {
          id: `section-${index}`,
          ...sectionTemplate,
          content,
          isComplete: content.length > 50 // Basic completeness check
        }
      })
    )

    // Extract legal references
    const legalReferences = this.extractLegalReferences(caseData, similarCases)

    const draft: JudgmentDraft = {
      id: `draft-${Date.now()}`,
      caseNumber: caseData.caseNumber,
      title: this.generateJudgmentTitle(caseData),
      judgmentType: caseData.caseType,
      sections,
      similarCases,
      legalReferences,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'draft',
      confidence: 0.8
    }

    return draft
  }

  /**
   * Generate content for a specific judgment section
   */
  private static async generateSectionContent(
    section: Omit<JudgmentSection, 'id' | 'content' | 'isComplete'>,
    caseData: any,
    similarCases: KenyaLawCase[]
  ): Promise<string> {
    
    switch (section.type) {
      case 'header':
        return this.generateHeaderContent(caseData)
      
      case 'parties':
        return this.generatePartiesContent(caseData)
      
      case 'background':
        return this.generateBackgroundContent(caseData)
      
      case 'issues':
        return this.generateIssuesContent(caseData)
      
      case 'analysis':
        return this.generateAnalysisContent(caseData, similarCases)
      
      case 'finding':
        return this.generateFindingContent(caseData)
      
      case 'orders':
        return this.generateOrdersContent(caseData)
      
      case 'costs':
        return this.generateCostsContent(caseData)
      
      default:
        return `[${section.title} - Content to be drafted]`
    }
  }

  private static generateHeaderContent(caseData: any): string {
    return `REPUBLIC OF KENYA

IN THE MAGISTRATE'S COURT AT [COURT_LOCATION]

${caseData.caseType.toUpperCase()} CASE NO. ${caseData.caseNumber}

${caseData.caseType === 'criminal' ? 'REPUBLIC' : caseData.parties.plaintiff || '[PLAINTIFF]'}
VS
${caseData.caseType === 'criminal' ? (caseData.parties.accused || '[ACCUSED]') : (caseData.parties.defendant || '[DEFENDANT]')}

JUDGMENT

Before: [MAGISTRATE_NAME], Senior Resident Magistrate

Date: ${new Date().toLocaleDateString('en-KE', { day: 'numeric', month: 'long', year: 'numeric' })}`
  }

  private static generatePartiesContent(caseData: any): string {
    if (caseData.caseType === 'criminal') {
      return `The Republic is represented by [PROSECUTOR_NAME], State Counsel.

The accused ${caseData.parties.accused || '[ACCUSED_NAME]'} is represented by [DEFENSE_COUNSEL] / appears in person.`
    } else {
      return `The Plaintiff ${caseData.parties.plaintiff || '[PLAINTIFF_NAME]'} is represented by [PLAINTIFF_COUNSEL].

The Defendant ${caseData.parties.defendant || '[DEFENDANT_NAME]'} is represented by [DEFENDANT_COUNSEL].`
    }
  }

  private static generateBackgroundContent(caseData: any): string {
    if (caseData.caseType === 'criminal') {
      const charges = caseData.charges?.join(', ') || '[CHARGES]'
      return `The accused is charged with ${charges}.

The prosecution case is that ${caseData.facts.substring(0, 300)}...

The accused pleaded [PLEA] to the charges.`
    } else {
      return `The plaintiff's claim is that ${caseData.facts.substring(0, 300)}...

The defendant's defense is [DEFENSE_SUMMARY].

[COUNTERCLAIM_SUMMARY if applicable]`
    }
  }

  private static generateIssuesContent(caseData: any): string {
    const issues = caseData.issues.map((issue: string, index: number) => 
      `${index + 1}. ${issue}`
    ).join('\n')

    return `The issues for determination in this case are:

${issues}

${caseData.caseType === 'criminal' ? 
  'The burden of proof lies on the prosecution to prove the case beyond reasonable doubt.' :
  'The burden of proof lies on the plaintiff to prove the case on a balance of probabilities.'}`
  }

  private static generateAnalysisContent(caseData: any, similarCases: KenyaLawCase[]): string {
    let analysis = `ANALYSIS OF EVIDENCE AND LAW

`

    // Add evidence analysis
    if (caseData.evidence && caseData.evidence.length > 0) {
      analysis += `Evidence Presented:
${caseData.evidence.map((evidence: string, index: number) => 
  `${index + 1}. ${evidence}`
).join('\n')}

`
    }

    // Add legal reasoning template
    if (caseData.caseType === 'criminal') {
      analysis += LEGAL_REASONING_TEMPLATES.CRIMINAL.BURDEN_OF_PROOF + '\n\n'
      analysis += LEGAL_REASONING_TEMPLATES.CRIMINAL.CREDIBILITY_ASSESSMENT + '\n\n'
    } else {
      analysis += LEGAL_REASONING_TEMPLATES.CIVIL.BALANCE_OF_PROBABILITIES + '\n\n'
    }

    // Add similar case references
    if (similarCases.length > 0) {
      analysis += `Relevant Case Law:

`
      similarCases.slice(0, 3).forEach(case_ => {
        analysis += `In ${case_.title} ${case_.citation}, the court held that ${case_.legalPrinciples[0] || 'relevant legal principle'}.

`
      })
    }

    return analysis
  }

  private static generateFindingContent(caseData: any): string {
    return `FINDING

Having considered the evidence presented and the submissions by counsel, this court finds as follows:

[COURT'S FINDINGS ON EACH ISSUE]

${caseData.caseType === 'criminal' ? 
  'The court finds the accused [GUILTY/NOT GUILTY] of the charges.' :
  'The court finds in favor of the [PLAINTIFF/DEFENDANT].'}`
  }

  private static generateOrdersContent(caseData: any): string {
    if (caseData.caseType === 'criminal') {
      return `ORDERS

IT IS HEREBY ORDERED THAT:

1. [SENTENCE/ACQUITTAL ORDER]

2. [ADDITIONAL ORDERS IF ANY]

Delivered in open court this ${new Date().toLocaleDateString('en-KE', { day: 'numeric', month: 'long', year: 'numeric' })}.`
    } else {
      return `ORDERS

IT IS HEREBY ORDERED THAT:

1. [MAIN RELIEF GRANTED/DENIED]

2. [ADDITIONAL ORDERS]

3. [COSTS ORDER - see separate section]`
    }
  }

  private static generateCostsContent(caseData: any): string {
    return `COSTS

The general rule is that costs follow the event. Having regard to the outcome of this case and the conduct of the parties, this court orders that:

[COSTS ORDER]`
  }

  private static generateJudgmentTitle(caseData: any): string {
    if (caseData.caseType === 'criminal') {
      return `Republic v. ${caseData.parties.accused || '[Accused]'} - Judgment`
    } else {
      return `${caseData.parties.plaintiff || '[Plaintiff]'} v. ${caseData.parties.defendant || '[Defendant]'} - Judgment`
    }
  }

  private static extractLegalReferences(caseData: any, similarCases: KenyaLawCase[]): LegalReference[] {
    const references: LegalReference[] = []

    // Constitutional references
    references.push({
      type: 'constitution',
      citation: 'Constitution of Kenya 2010, Article 50',
      relevantText: 'Every person has the right to a fair trial',
      application: 'Fundamental right to fair trial'
    })

    // Statutory references based on case type
    if (caseData.caseType === 'criminal') {
      references.push({
        type: 'statute',
        citation: 'Criminal Procedure Code (Cap 75)',
        relevantText: 'Procedure for criminal trials',
        application: 'Procedural requirements for criminal cases'
      })

      if (caseData.charges?.some((c: string) => c.toLowerCase().includes('theft'))) {
        references.push({
          type: 'statute',
          citation: 'Penal Code (Cap 63), Sections 268-279',
          relevantText: 'Theft and related offenses',
          application: 'Definition and elements of theft'
        })
      }
    } else {
      references.push({
        type: 'statute',
        citation: 'Civil Procedure Act (Cap 21)',
        relevantText: 'Procedure for civil cases',
        application: 'Procedural requirements for civil litigation'
      })
    }

    // Case law references
    similarCases.slice(0, 3).forEach(case_ => {
      references.push({
        type: 'case_law',
        citation: `${case_.title} ${case_.citation}`,
        relevantText: case_.legalPrinciples[0] || case_.summary,
        application: 'Precedent for similar legal issues'
      })
    })

    return references
  }

  /**
   * Update a judgment section
   */
  static updateJudgmentSection(
    draft: JudgmentDraft,
    sectionId: string,
    content: string
  ): JudgmentDraft {
    const updatedSections = draft.sections.map(section => 
      section.id === sectionId 
        ? { ...section, content, isComplete: content.length > 50 }
        : section
    )

    return {
      ...draft,
      sections: updatedSections,
      updatedAt: new Date().toISOString()
    }
  }

  /**
   * Generate suggestions for improving a judgment section
   */
  static generateSectionSuggestions(section: JudgmentSection, caseType: string): string[] {
    const suggestions: string[] = []

    // Check completeness
    if (section.content.length < 100) {
      suggestions.push('Consider expanding this section with more detail')
    }

    // Check for placeholders
    if (section.content.includes('[') && section.content.includes(']')) {
      suggestions.push('Replace placeholder text with actual case information')
    }

    // Type-specific suggestions
    if (section.type === 'analysis' && caseType === 'criminal') {
      if (!section.content.toLowerCase().includes('beyond reasonable doubt')) {
        suggestions.push('Consider referencing the standard of proof beyond reasonable doubt')
      }
    }

    if (section.type === 'analysis' && caseType === 'civil') {
      if (!section.content.toLowerCase().includes('balance of probabilities')) {
        suggestions.push('Consider referencing the balance of probabilities standard')
      }
    }

    return suggestions
  }

  /**
   * Validate judgment completeness
   */
  static validateJudgment(draft: JudgmentDraft): { isValid: boolean; issues: string[] } {
    const issues: string[] = []

    // Check required sections
    const requiredSections = draft.sections.filter(s => s.isRequired)
    const incompleteSections = requiredSections.filter(s => !s.isComplete)

    if (incompleteSections.length > 0) {
      issues.push(`Incomplete required sections: ${incompleteSections.map(s => s.title).join(', ')}`)
    }

    // Check for placeholders
    const sectionsWithPlaceholders = draft.sections.filter(s => 
      s.content.includes('[') && s.content.includes(']')
    )

    if (sectionsWithPlaceholders.length > 0) {
      issues.push(`Sections with placeholder text: ${sectionsWithPlaceholders.map(s => s.title).join(', ')}`)
    }

    return {
      isValid: issues.length === 0,
      issues
    }
  }
}
