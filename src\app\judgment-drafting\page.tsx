'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardHeader, CardContent, InfoCard } from '@/components/ui/Card'
import { FormField, Label, Input, Select, Textarea } from '@/components/ui/Form'
import { useAuth, withAuth } from '@/contexts/AuthContext'
import { JudgmentDraftingEngine, type JudgmentDraft, type JudgmentSection } from '@/lib/judgment-drafting'
import { formatLegalDate } from '@/lib/utils'

function JudgmentDraftingPage() {
  const { user } = useAuth()
  const [currentDraft, setCurrentDraft] = useState<JudgmentDraft | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [editingSection, setEditingSection] = useState<string | null>(null)
  const [caseData, setCaseData] = useState({
    caseNumber: '',
    caseType: 'criminal' as 'criminal' | 'civil' | 'family' | 'commercial',
    plaintiff: '',
    defendant: '',
    accused: '',
    facts: '',
    charges: '',
    issues: '',
    evidence: ''
  })

  const caseTypes = [
    { value: 'criminal', label: 'Criminal Case' },
    { value: 'civil', label: 'Civil Case' },
    { value: 'family', label: 'Family Case' },
    { value: 'commercial', label: 'Commercial Case' }
  ]

  const handleInputChange = (field: string, value: string) => {
    setCaseData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleGenerateDraft = async () => {
    if (!caseData.caseNumber || !caseData.facts) {
      alert('Please fill in the required fields: Case Number and Facts')
      return
    }

    setIsGenerating(true)
    
    try {
      // Prepare data for judgment generation
      const judgmentData = {
        caseNumber: caseData.caseNumber,
        caseType: caseData.caseType,
        parties: {
          plaintiff: caseData.plaintiff,
          defendant: caseData.defendant,
          accused: caseData.accused
        },
        facts: caseData.facts,
        charges: caseData.charges ? caseData.charges.split(',').map(c => c.trim()) : [],
        issues: caseData.issues ? caseData.issues.split('\n').map(i => i.trim()).filter(i => i) : [],
        evidence: caseData.evidence ? caseData.evidence.split('\n').map(e => e.trim()).filter(e => e) : []
      }

      // Simulate AI generation delay
      await new Promise(resolve => setTimeout(resolve, 4000))
      
      const draft = await JudgmentDraftingEngine.createJudgmentDraft(judgmentData)
      setCurrentDraft(draft)
    } catch (error) {
      console.error('Draft generation error:', error)
      alert('Error generating judgment draft. Please try again.')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleSectionEdit = (sectionId: string, content: string) => {
    if (currentDraft) {
      const updatedDraft = JudgmentDraftingEngine.updateJudgmentSection(currentDraft, sectionId, content)
      setCurrentDraft(updatedDraft)
      setEditingSection(null)
    }
  }

  const getSectionIcon = (type: string) => {
    switch (type) {
      case 'header':
        return (
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        )
      case 'analysis':
        return (
          <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        )
      case 'finding':
        return (
          <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
      default:
        return (
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        )
    }
  }

  const exportJudgment = () => {
    if (!currentDraft) return

    const content = currentDraft.sections.map(section => 
      `${section.title.toUpperCase()}\n\n${section.content}\n\n`
    ).join('')
    
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `judgment-${currentDraft.caseNumber.replace(/\//g, '-')}-${new Date().toISOString().split('T')[0]}.txt`
    a.click()
    URL.revokeObjectURL(url)
  }

  const validation = currentDraft ? JudgmentDraftingEngine.validateJudgment(currentDraft) : null

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button 
                variant="secondary" 
                onClick={() => window.history.back()}
                className="flex items-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Back to Dashboard
              </Button>
              <h1 className="text-2xl font-bold text-gray-900">AI Judgment Drafting</h1>
            </div>
            <div className="flex items-center space-x-4">
              {currentDraft && (
                <Button variant="primary" onClick={exportJudgment}>
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Export Judgment
                </Button>
              )}
              <div className="text-right">
                <p className="text-sm text-gray-600">
                  {user?.displayName}
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {!currentDraft ? (
          /* Case Information Input */
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">Case Information for Judgment Drafting</h3>
                <p className="text-sm text-gray-600">
                  Enter case details to generate an AI-assisted judgment draft with legal reasoning and precedent integration
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField>
                      <Label htmlFor="caseNumber" required>Case Number</Label>
                      <Input
                        id="caseNumber"
                        value={caseData.caseNumber}
                        onChange={(e) => handleInputChange('caseNumber', e.target.value)}
                        placeholder="e.g., CR 45/2024 MC"
                      />
                    </FormField>
                    
                    <FormField>
                      <Label htmlFor="caseType" required>Case Type</Label>
                      <Select
                        id="caseType"
                        value={caseData.caseType}
                        onChange={(e) => handleInputChange('caseType', e.target.value)}
                        options={caseTypes}
                      />
                    </FormField>
                  </div>

                  {/* Party Information */}
                  <div className="border-t pt-4">
                    <h4 className="text-md font-semibold text-gray-900 mb-3">Party Information</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {caseData.caseType === 'criminal' ? (
                        <FormField>
                          <Label htmlFor="accused">Accused Name</Label>
                          <Input
                            id="accused"
                            value={caseData.accused}
                            onChange={(e) => handleInputChange('accused', e.target.value)}
                            placeholder="Name of accused person"
                          />
                        </FormField>
                      ) : (
                        <>
                          <FormField>
                            <Label htmlFor="plaintiff">Plaintiff</Label>
                            <Input
                              id="plaintiff"
                              value={caseData.plaintiff}
                              onChange={(e) => handleInputChange('plaintiff', e.target.value)}
                              placeholder="Plaintiff name"
                            />
                          </FormField>
                          <FormField>
                            <Label htmlFor="defendant">Defendant</Label>
                            <Input
                              id="defendant"
                              value={caseData.defendant}
                              onChange={(e) => handleInputChange('defendant', e.target.value)}
                              placeholder="Defendant name"
                            />
                          </FormField>
                        </>
                      )}
                    </div>
                  </div>

                  <FormField>
                    <Label htmlFor="facts" required>Case Facts</Label>
                    <Textarea
                      id="facts"
                      value={caseData.facts}
                      onChange={(e) => handleInputChange('facts', e.target.value)}
                      placeholder="Enter the facts of the case..."
                      rows={4}
                    />
                  </FormField>

                  {caseData.caseType === 'criminal' && (
                    <FormField>
                      <Label htmlFor="charges">Charges</Label>
                      <Input
                        id="charges"
                        value={caseData.charges}
                        onChange={(e) => handleInputChange('charges', e.target.value)}
                        placeholder="e.g., Theft, Assault (separate with commas)"
                        helpText="Enter charges separated by commas"
                      />
                    </FormField>
                  )}

                  <FormField>
                    <Label htmlFor="issues">Legal Issues</Label>
                    <Textarea
                      id="issues"
                      value={caseData.issues}
                      onChange={(e) => handleInputChange('issues', e.target.value)}
                      placeholder="Enter each legal issue on a new line..."
                      rows={3}
                      helpText="Enter each issue on a separate line"
                    />
                  </FormField>

                  <FormField>
                    <Label htmlFor="evidence">Evidence Summary</Label>
                    <Textarea
                      id="evidence"
                      value={caseData.evidence}
                      onChange={(e) => handleInputChange('evidence', e.target.value)}
                      placeholder="Enter each piece of evidence on a new line..."
                      rows={3}
                      helpText="Enter each piece of evidence on a separate line"
                    />
                  </FormField>

                  <Button
                    variant="primary"
                    size="large"
                    onClick={handleGenerateDraft}
                    loading={isGenerating}
                    className="w-full"
                    disabled={!caseData.caseNumber || !caseData.facts}
                  >
                    {isGenerating ? 'Generating AI-Assisted Judgment...' : 'Generate Judgment Draft'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {isGenerating && (
              <Card className="mt-6">
                <CardContent>
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <svg className="w-10 h-10 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Generating Judgment Draft...
                    </h3>
                    <p className="text-gray-600 mb-4">
                      AI is analyzing case information, finding similar precedents, and drafting judgment sections
                    </p>
                    <div className="max-w-md mx-auto">
                      <div className="text-sm text-gray-500 space-y-1">
                        <p>✓ Analyzing case facts and legal issues</p>
                        <p>✓ Searching Kenya Law Reports for precedents</p>
                        <p>✓ Applying legal reasoning templates</p>
                        <p>⏳ Generating judgment sections...</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        ) : (
          /* Judgment Draft Editor */
          <div className="space-y-6">
            {/* Draft Header */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{currentDraft.title}</h3>
                    <p className="text-sm text-gray-600">
                      Draft created: {formatLegalDate(new Date(currentDraft.createdAt))} • 
                      Confidence: {Math.round(currentDraft.confidence * 100)}%
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="secondary" onClick={() => setCurrentDraft(null)}>
                      New Draft
                    </Button>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      currentDraft.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                      currentDraft.status === 'review' ? 'bg-blue-100 text-blue-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {currentDraft.status.toUpperCase()}
                    </span>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Validation Status */}
            {validation && !validation.isValid && (
              <InfoCard type="warning" title="Judgment Validation Issues">
                <ul className="list-disc list-inside space-y-1">
                  {validation.issues.map((issue, index) => (
                    <li key={index}>{issue}</li>
                  ))}
                </ul>
              </InfoCard>
            )}

            {/* Similar Cases */}
            {currentDraft.similarCases.length > 0 && (
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold text-gray-900">Similar Cases & Precedents</h3>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {currentDraft.similarCases.slice(0, 4).map(case_ => (
                      <div key={case_.id} className="p-3 border border-gray-200 rounded-lg">
                        <h4 className="font-medium text-gray-900">{case_.title}</h4>
                        <p className="text-sm text-blue-600">{case_.citation}</p>
                        <p className="text-xs text-gray-500 mt-1">{case_.court}</p>
                        {case_.legalPrinciples[0] && (
                          <p className="text-sm text-gray-700 mt-2 italic">
                            "{case_.legalPrinciples[0]}"
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Judgment Sections */}
            <div className="space-y-4">
              {currentDraft.sections.map(section => (
                <Card key={section.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getSectionIcon(section.type)}
                        <div>
                          <h4 className="text-lg font-semibold text-gray-900">{section.title}</h4>
                          <div className="flex items-center space-x-2">
                            {section.isRequired && (
                              <span className="text-xs text-red-600">Required</span>
                            )}
                            <span className={`text-xs ${section.isComplete ? 'text-green-600' : 'text-yellow-600'}`}>
                              {section.isComplete ? 'Complete' : 'Incomplete'}
                            </span>
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="secondary"
                        size="small"
                        onClick={() => setEditingSection(section.id)}
                      >
                        Edit
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {editingSection === section.id ? (
                      <div className="space-y-4">
                        <Textarea
                          value={section.content}
                          onChange={(e) => {
                            const updatedSections = currentDraft.sections.map(s =>
                              s.id === section.id ? { ...s, content: e.target.value } : s
                            )
                            setCurrentDraft({ ...currentDraft, sections: updatedSections })
                          }}
                          rows={10}
                          className="font-mono text-sm"
                        />
                        <div className="flex space-x-2">
                          <Button
                            variant="success"
                            size="small"
                            onClick={() => handleSectionEdit(section.id, section.content)}
                          >
                            Save Changes
                          </Button>
                          <Button
                            variant="secondary"
                            size="small"
                            onClick={() => setEditingSection(null)}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                            {section.content}
                          </pre>
                        </div>
                        
                        {section.suggestions.length > 0 && (
                          <div>
                            <h5 className="font-medium text-gray-900 mb-2">AI Suggestions:</h5>
                            <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                              {section.suggestions.map((suggestion, index) => (
                                <li key={index}>{suggestion}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Legal References */}
            {currentDraft.legalReferences.length > 0 && (
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold text-gray-900">Legal References</h3>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {currentDraft.legalReferences.map((ref, index) => (
                      <div key={index} className="border-l-4 border-blue-500 pl-4">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            ref.type === 'constitution' ? 'bg-purple-100 text-purple-800' :
                            ref.type === 'statute' ? 'bg-blue-100 text-blue-800' :
                            ref.type === 'case_law' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {ref.type.replace('_', ' ').toUpperCase()}
                          </span>
                          <span className="font-medium text-gray-900">{ref.citation}</span>
                        </div>
                        <p className="text-sm text-gray-700 mb-1">{ref.relevantText}</p>
                        <p className="text-xs text-gray-500">{ref.application}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Help Information */}
        <div className="mt-8">
          <InfoCard type="info" title="AI Judgment Drafting Features">
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li><strong>Intelligent Templates:</strong> Uses appropriate judgment structure based on case type</li>
              <li><strong>Precedent Integration:</strong> Automatically finds and references similar cases from Kenya Law Reports</li>
              <li><strong>Legal Reasoning:</strong> Applies established legal principles and reasoning templates</li>
              <li><strong>Section-by-Section Guidance:</strong> Provides suggestions and precedents for each judgment section</li>
              <li><strong>Validation:</strong> Checks for completeness and identifies areas needing attention</li>
              <li><strong>Kenyan Law Compliance:</strong> Ensures adherence to Kenyan legal framework and procedures</li>
            </ul>
          </InfoCard>
        </div>
      </main>
    </div>
  )
}

// Export with authentication protection
export default withAuth(JudgmentDraftingPage, ['draft_judgment'])
