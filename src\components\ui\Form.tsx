import React from 'react'
import { cn } from '@/lib/utils'

interface FormFieldProps {
  children: React.ReactNode
  className?: string
}

interface LabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  required?: boolean
  children: React.ReactNode
}

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string
  helpText?: string
}

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: string
  helpText?: string
}

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  error?: string
  helpText?: string
  options: Array<{ value: string; label: string; disabled?: boolean }>
  placeholder?: string
}

/**
 * Form field wrapper for consistent spacing and layout
 */
export function FormField({ children, className }: FormFieldProps) {
  return (
    <div className={cn('space-y-2', className)}>
      {children}
    </div>
  )
}

/**
 * Form label with required indicator
 */
export function Label({ required, children, className, ...props }: LabelProps) {
  return (
    <label className={cn('form-label', className)} {...props}>
      {children}
      {required && <span className="text-red-500 ml-1">*</span>}
    </label>
  )
}

/**
 * Text input with error handling and help text
 */
export function Input({ error, helpText, className, ...props }: InputProps) {
  return (
    <div>
      <input
        className={cn(
          'form-input',
          error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
          className
        )}
        {...props}
      />
      {error && (
        <p className="mt-2 text-sm text-red-600 flex items-center">
          <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {error}
        </p>
      )}
      {helpText && !error && (
        <p className="mt-2 text-sm text-gray-600">
          {helpText}
        </p>
      )}
    </div>
  )
}

/**
 * Textarea with error handling and help text
 */
export function Textarea({ error, helpText, className, rows = 4, ...props }: TextareaProps) {
  return (
    <div>
      <textarea
        rows={rows}
        className={cn(
          'form-input resize-vertical min-h-[100px]',
          error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
          className
        )}
        {...props}
      />
      {error && (
        <p className="mt-2 text-sm text-red-600 flex items-center">
          <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {error}
        </p>
      )}
      {helpText && !error && (
        <p className="mt-2 text-sm text-gray-600">
          {helpText}
        </p>
      )}
    </div>
  )
}

/**
 * Select dropdown with error handling and help text
 */
export function Select({ error, helpText, options, placeholder, className, ...props }: SelectProps) {
  return (
    <div>
      <select
        className={cn(
          'form-input',
          error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
          className
        )}
        {...props}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option
            key={option.value}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </option>
        ))}
      </select>
      {error && (
        <p className="mt-2 text-sm text-red-600 flex items-center">
          <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {error}
        </p>
      )}
      {helpText && !error && (
        <p className="mt-2 text-sm text-gray-600">
          {helpText}
        </p>
      )}
    </div>
  )
}

/**
 * Checkbox with label
 */
interface CheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string
  error?: string
  helpText?: string
}

export function Checkbox({ label, error, helpText, className, ...props }: CheckboxProps) {
  return (
    <div>
      <div className="flex items-start space-x-3">
        <input
          type="checkbox"
          className={cn(
            'mt-1 h-5 w-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 focus:ring-2',
            error && 'border-red-500',
            className
          )}
          {...props}
        />
        <div className="flex-1">
          <label className="text-base font-medium text-gray-700 cursor-pointer">
            {label}
          </label>
          {helpText && !error && (
            <p className="mt-1 text-sm text-gray-600">
              {helpText}
            </p>
          )}
        </div>
      </div>
      {error && (
        <p className="mt-2 text-sm text-red-600 flex items-center">
          <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {error}
        </p>
      )}
    </div>
  )
}

/**
 * Radio button group
 */
interface RadioOption {
  value: string
  label: string
  description?: string
  disabled?: boolean
}

interface RadioGroupProps {
  name: string
  options: RadioOption[]
  value?: string
  onChange: (value: string) => void
  error?: string
  helpText?: string
  className?: string
}

export function RadioGroup({
  name,
  options,
  value,
  onChange,
  error,
  helpText,
  className
}: RadioGroupProps) {
  return (
    <div className={className}>
      <div className="space-y-3">
        {options.map((option) => (
          <div key={option.value} className="flex items-start space-x-3">
            <input
              type="radio"
              id={`${name}-${option.value}`}
              name={name}
              value={option.value}
              checked={value === option.value}
              onChange={(e) => onChange(e.target.value)}
              disabled={option.disabled}
              className={cn(
                'mt-1 h-5 w-5 text-blue-600 border-2 border-gray-300 focus:ring-blue-500 focus:ring-2',
                error && 'border-red-500'
              )}
            />
            <div className="flex-1">
              <label
                htmlFor={`${name}-${option.value}`}
                className="text-base font-medium text-gray-700 cursor-pointer"
              >
                {option.label}
              </label>
              {option.description && (
                <p className="mt-1 text-sm text-gray-600">
                  {option.description}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
      {error && (
        <p className="mt-2 text-sm text-red-600 flex items-center">
          <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {error}
        </p>
      )}
      {helpText && !error && (
        <p className="mt-2 text-sm text-gray-600">
          {helpText}
        </p>
      )}
    </div>
  )
}

/**
 * Form section with title and description
 */
interface FormSectionProps {
  title: string
  description?: string
  children: React.ReactNode
  className?: string
}

export function FormSection({ title, description, children, className }: FormSectionProps) {
  return (
    <div className={cn('space-y-6', className)}>
      <div>
        <h3 className="text-xl font-semibold text-gray-900">
          {title}
        </h3>
        {description && (
          <p className="mt-2 text-base text-gray-600">
            {description}
          </p>
        )}
      </div>
      <div className="space-y-4">
        {children}
      </div>
    </div>
  )
}
