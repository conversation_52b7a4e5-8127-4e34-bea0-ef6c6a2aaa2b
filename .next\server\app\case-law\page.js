/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/case-law/page";
exports.ids = ["app/case-law/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcase-law%2Fpage&page=%2Fcase-law%2Fpage&appPaths=%2Fcase-law%2Fpage&pagePath=private-next-app-dir%2Fcase-law%2Fpage.tsx&appDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcase-law%2Fpage&page=%2Fcase-law%2Fpage&appPaths=%2Fcase-law%2Fpage&pagePath=private-next-app-dir%2Fcase-law%2Fpage.tsx&appDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/case-law/page.tsx */ \"(rsc)/./src/app/case-law/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'case-law',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/case-law/page\",\n        pathname: \"/case-law\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcase-law%2Fpage&page=%2Fcase-law%2Fpage&appPaths=%2Fcase-law%2Fpage&pagePath=private-next-app-dir%2Fcase-law%2Fpage.tsx&appDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC1pbnRlciU1QyUyMiU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBMkkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHNhcmNoXFxcXERlc2t0b3BcXFxcS2VzaVRyYWNrXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxBdXRoQ29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Ccase-law%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Ccase-law%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/case-law/page.tsx */ \"(rsc)/./src/app/case-law/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Nhc2UtbGF3JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUF1RyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcc2FyY2hcXFxcRGVza3RvcFxcXFxLZXNpVHJhY2tcXFxcc3JjXFxcXGFwcFxcXFxjYXNlLWxhd1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Ccase-law%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FyY2hcXERlc2t0b3BcXEtlc2lUcmFja1xcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/case-law/page.tsx":
/*!***********************************!*\
  !*** ./src/app/case-law/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\KesiTrack\\src\\app\\case-law\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8fb898129f22\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhcmNoXFxEZXNrdG9wXFxLZXNpVHJhY2tcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhmYjg5ODEyOWYyMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"KesiTrack - Legal Case Management System\",\n    description: \"AI-powered legal case management system for the Republic of Kenya Judiciary\",\n    keywords: \"legal, case management, Kenya, judiciary, court, magistrate\",\n    authors: [\n        {\n            name: \"Republic of Kenya - Judiciary\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} h-full bg-gray-50 font-sans antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth),
/* harmony export */   withAuth: () => (/* binding */ withAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\KesiTrack\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\KesiTrack\\src\\contexts\\AuthContext.tsx",
"useAuth",
);const withAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call withAuth() from the server but withAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\KesiTrack\\src\\contexts\\AuthContext.tsx",
"withAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC1pbnRlciU1QyUyMiU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBMkkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHNhcmNoXFxcXERlc2t0b3BcXFxcS2VzaVRyYWNrXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxBdXRoQ29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Ccase-law%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Ccase-law%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/case-law/page.tsx */ \"(ssr)/./src/app/case-law/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Nhc2UtbGF3JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUF1RyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcc2FyY2hcXFxcRGVza3RvcFxcXFxLZXNpVHJhY2tcXFxcc3JjXFxcXGFwcFxcXFxjYXNlLWxhd1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Ccase-law%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/case-law/page.tsx":
/*!***********************************!*\
  !*** ./src/app/case-law/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Form */ \"(ssr)/./src/components/ui/Form.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_kenya_law_reports__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/kenya-law-reports */ \"(ssr)/./src/lib/kenya-law-reports.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction CaseLawPage() {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCase, setSelectedCase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        court: '',\n        category: '',\n        dateFrom: '',\n        dateTo: ''\n    });\n    const courts = [\n        {\n            value: '',\n            label: 'All Courts'\n        },\n        {\n            value: 'Supreme Court',\n            label: 'Supreme Court of Kenya'\n        },\n        {\n            value: 'Court of Appeal',\n            label: 'Court of Appeal of Kenya'\n        },\n        {\n            value: 'High Court',\n            label: 'High Court of Kenya'\n        },\n        {\n            value: 'Commercial Court',\n            label: 'Commercial Court'\n        },\n        {\n            value: 'Family Division',\n            label: 'Family Division'\n        },\n        {\n            value: 'Employment Court',\n            label: 'Employment and Labour Relations Court'\n        }\n    ];\n    const categories = [\n        {\n            value: '',\n            label: 'All Categories'\n        },\n        {\n            value: 'constitutional',\n            label: 'Constitutional Law'\n        },\n        {\n            value: 'criminal',\n            label: 'Criminal Law'\n        },\n        {\n            value: 'civil',\n            label: 'Civil Law'\n        },\n        {\n            value: 'commercial',\n            label: 'Commercial Law'\n        },\n        {\n            value: 'family',\n            label: 'Family Law'\n        },\n        {\n            value: 'administrative',\n            label: 'Administrative Law'\n        }\n    ];\n    const handleSearch = async ()=>{\n        if (!searchQuery.trim()) {\n            alert('Please enter a search term');\n            return;\n        }\n        setIsSearching(true);\n        try {\n            // Simulate search delay\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            const results = _lib_kenya_law_reports__WEBPACK_IMPORTED_MODULE_6__.KenyaLawReportsEngine.searchCases(searchQuery, {\n                court: filters.court || undefined,\n                category: filters.category || undefined,\n                dateFrom: filters.dateFrom || undefined,\n                dateTo: filters.dateTo || undefined,\n                limit: 20\n            });\n            setSearchResults(results);\n        } catch (error) {\n            console.error('Search error:', error);\n            alert('Error searching case law. Please try again.');\n        } finally{\n            setIsSearching(false);\n        }\n    };\n    const handleQuickSearch = (query)=>{\n        setSearchQuery(query);\n        setSearchResults(_lib_kenya_law_reports__WEBPACK_IMPORTED_MODULE_6__.KenyaLawReportsEngine.searchCases(query, {\n            limit: 10\n        }));\n    };\n    const getCategoryColor = (category)=>{\n        switch(category){\n            case 'constitutional':\n                return 'bg-purple-100 text-purple-800';\n            case 'criminal':\n                return 'bg-red-100 text-red-800';\n            case 'civil':\n                return 'bg-blue-100 text-blue-800';\n            case 'commercial':\n                return 'bg-green-100 text-green-800';\n            case 'family':\n                return 'bg-pink-100 text-pink-800';\n            case 'administrative':\n                return 'bg-yellow-100 text-yellow-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getCourtIcon = (court)=>{\n        if (court.includes('Supreme Court')) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5 text-purple-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M5 3l14 9-14 9V3z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, this);\n        } else if (court.includes('Court of Appeal')) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5 text-blue-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5 text-gray-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"secondary\",\n                                        onClick: ()=>window.history.back(),\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 19l-7-7 7-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Kenya Law Reports\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: user?.displayName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                children: selectedCase ? /* Case Details View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Case Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"secondary\",\n                                    onClick: ()=>setSelectedCase(null),\n                                    children: \"Back to Search Results\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                        children: selectedCase.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-600 font-medium\",\n                                                        children: selectedCase.citation\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    getCourtIcon(selectedCase.court),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(selectedCase.category)}`,\n                                                        children: selectedCase.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-700\",\n                                                                children: \"Court:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: selectedCase.court\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-700\",\n                                                                children: \"Date:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatLegalDate)(new Date(selectedCase.date))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-700\",\n                                                                children: \"Judges:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: selectedCase.judges.join(', ')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedCase.relevanceScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-700\",\n                                                                children: \"Relevance:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: [\n                                                                    Math.round(selectedCase.relevanceScore),\n                                                                    \"% match\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-2\",\n                                                        children: \"Summary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-700\",\n                                                        children: selectedCase.summary\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-2\",\n                                                        children: \"Headnotes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc list-inside space-y-1 text-gray-700\",\n                                                        children: selectedCase.headnotes.map((headnote, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: headnote\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-2\",\n                                                        children: \"Legal Principles\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: selectedCase.legalPrinciples.map((principle, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-blue-50 border-l-4 border-blue-500 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-800\",\n                                                                    children: principle\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-2\",\n                                                        children: \"Keywords\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: selectedCase.keywords.map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-sm cursor-pointer hover:bg-gray-200\",\n                                                                onClick: ()=>handleQuickSearch(keyword),\n                                                                children: keyword\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900 mb-2\",\n                                                        children: \"Full Text (Preview)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"whitespace-pre-wrap text-sm text-gray-700 font-mono\",\n                                                            children: [\n                                                                selectedCase.fullText.substring(0, 1000),\n                                                                \"...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"primary\",\n                                                        className: \"mt-2\",\n                                                        children: \"View Full Text\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 11\n                }, this) : /* Search Interface */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Search Kenya Law Reports\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Search for case law, precedents, and legal principles from Kenyan courts\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"search\",\n                                                        children: \"Search Terms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                id: \"search\",\n                                                                value: searchQuery,\n                                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                                placeholder: \"Enter keywords, case names, legal principles...\",\n                                                                className: \"flex-1\",\n                                                                onKeyPress: (e)=>e.key === 'Enter' && handleSearch()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"primary\",\n                                                                onClick: handleSearch,\n                                                                loading: isSearching,\n                                                                disabled: !searchQuery.trim(),\n                                                                children: isSearching ? 'Searching...' : 'Search'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"court\",\n                                                                children: \"Court\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                                id: \"court\",\n                                                                value: filters.court,\n                                                                onChange: (e)=>setFilters((prev)=>({\n                                                                            ...prev,\n                                                                            court: e.target.value\n                                                                        })),\n                                                                options: courts\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"category\",\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                                id: \"category\",\n                                                                value: filters.category,\n                                                                onChange: (e)=>setFilters((prev)=>({\n                                                                            ...prev,\n                                                                            category: e.target.value\n                                                                        })),\n                                                                options: categories\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"dateFrom\",\n                                                                children: \"From Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                id: \"dateFrom\",\n                                                                type: \"date\",\n                                                                value: filters.dateFrom,\n                                                                onChange: (e)=>setFilters((prev)=>({\n                                                                            ...prev,\n                                                                            dateFrom: e.target.value\n                                                                        }))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"dateTo\",\n                                                                children: \"To Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                id: \"dateTo\",\n                                                                type: \"date\",\n                                                                value: filters.dateTo,\n                                                                onChange: (e)=>setFilters((prev)=>({\n                                                                            ...prev,\n                                                                            dateTo: e.target.value\n                                                                        }))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        children: \"Quick Searches\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 mt-2\",\n                                                        children: [\n                                                            'bail',\n                                                            'theft',\n                                                            'employment',\n                                                            'child custody',\n                                                            'constitutional rights',\n                                                            'contract breach'\n                                                        ].map((term)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"secondary\",\n                                                                size: \"small\",\n                                                                onClick: ()=>handleQuickSearch(term),\n                                                                children: term\n                                                            }, term, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, this),\n                        searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: [\n                                            \"Search Results (\",\n                                            searchResults.length,\n                                            \" cases found)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: searchResults.map((case_)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200 cursor-pointer\",\n                                                onClick: ()=>setSelectedCase(case_),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-lg font-semibold text-gray-900 hover:text-blue-600\",\n                                                                        children: case_.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                        lineNumber: 372,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-blue-600 font-medium\",\n                                                                        children: case_.citation\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                        lineNumber: 375,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 ml-4\",\n                                                                children: [\n                                                                    getCourtIcon(case_.court),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(case_.category)}`,\n                                                                        children: case_.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                        lineNumber: 379,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    case_.relevanceScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            Math.round(case_.relevanceScore),\n                                                                            \"% match\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600 mb-2\",\n                                                        children: [\n                                                            case_.court,\n                                                            \" • \",\n                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatLegalDate)(new Date(case_.date))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-700 mb-3\",\n                                                        children: case_.summary\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: [\n                                                            case_.keywords.slice(0, 5).map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs\",\n                                                                    children: keyword\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 29\n                                                                }, this)),\n                                                            case_.keywords.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    case_.keywords.length - 5,\n                                                                    \" more\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, case_.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 15\n                        }, this),\n                        searchResults.length === 0 && !isSearching && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: \"Recent Cases\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: _lib_kenya_law_reports__WEBPACK_IMPORTED_MODULE_6__.KenyaLawReportsEngine.getRecentCases(5).map((case_)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow duration-200 cursor-pointer\",\n                                                        onClick: ()=>setSelectedCase(case_),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 hover:text-blue-600\",\n                                                                children: case_.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-blue-600\",\n                                                                children: case_.citation\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    case_.court,\n                                                                    \" • \",\n                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatLegalDate)(new Date(case_.date))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, case_.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: \"Landmark Cases\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: _lib_kenya_law_reports__WEBPACK_IMPORTED_MODULE_6__.KenyaLawReportsEngine.getLandmarkCases().slice(0, 5).map((case_)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow duration-200 cursor-pointer\",\n                                                        onClick: ()=>setSelectedCase(case_),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900 hover:text-blue-600\",\n                                                                children: case_.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-blue-600\",\n                                                                children: case_.citation\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    case_.court,\n                                                                    \" • \",\n                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatLegalDate)(new Date(case_.date))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, case_.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.InfoCard, {\n                            type: \"info\",\n                            title: \"Search Tips\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Keywords:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 21\n                                            }, this),\n                                            ' Use specific legal terms like \"bail\", \"custody\", \"contract breach\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Case Names:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 21\n                                            }, this),\n                                            ' Search by party names like \"Republic v. John Doe\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Citations:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 21\n                                            }, this),\n                                            ' Find cases by citation like \"[2023] eKLR\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Legal Principles:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 21\n                                            }, this),\n                                            ' Search for concepts like \"best interests of child\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Filters:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" Use court and category filters to narrow results\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\case-law\\\\page.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, this);\n}\n// Export with authentication protection\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.withAuth)(CaseLawPage, [\n    'view_all_cases'\n]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/case-law/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   ButtonGroup: () => (/* binding */ ButtonGroup),\n/* harmony export */   IconButton: () => (/* binding */ IconButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n/**\n * Large, accessible button component designed for non-technical users\n * Features:\n * - Large touch targets (minimum 44px)\n * - Clear visual feedback\n * - Loading states\n * - High contrast colors\n * - Keyboard navigation support\n */ function Button({ variant = 'primary', size = 'medium', loading = false, icon, children, className, disabled, ...props }) {\n    const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed';\n    const variantClasses = {\n        primary: 'btn-primary focus:ring-blue-300',\n        secondary: 'btn-secondary focus:ring-gray-300',\n        success: 'btn-success focus:ring-green-300',\n        warning: 'btn-warning focus:ring-yellow-300',\n        danger: 'btn-danger focus:ring-red-300'\n    };\n    const sizeClasses = {\n        small: 'px-4 py-2 text-base min-h-[40px]',\n        medium: 'px-6 py-3 text-lg min-h-[48px]',\n        large: 'px-8 py-4 text-xl min-h-[56px]'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variantClasses[variant], sizeClasses[size], className),\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-3 h-5 w-5\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            icon && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 80,\n                columnNumber: 28\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Icon-only button for actions like edit, delete, etc.\n */ function IconButton({ variant = 'secondary', size = 'medium', loading = false, icon, className, disabled, 'aria-label': ariaLabel, ...props }) {\n    const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed';\n    const variantClasses = {\n        primary: 'btn-primary focus:ring-blue-300',\n        secondary: 'btn-secondary focus:ring-gray-300',\n        success: 'btn-success focus:ring-green-300',\n        warning: 'btn-warning focus:ring-yellow-300',\n        danger: 'btn-danger focus:ring-red-300'\n    };\n    const sizeClasses = {\n        small: 'p-2 min-h-[40px] min-w-[40px]',\n        medium: 'p-3 min-h-[48px] min-w-[48px]',\n        large: 'p-4 min-h-[56px] min-w-[56px]'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variantClasses[variant], sizeClasses[size], className),\n        disabled: disabled || loading,\n        \"aria-label\": ariaLabel,\n        ...props,\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"animate-spin h-5 w-5\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    className: \"opacity-25\",\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"10\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    className: \"opacity-75\",\n                    fill: \"currentColor\",\n                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n            lineNumber: 128,\n            columnNumber: 9\n        }, this) : icon\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Button group for related actions\n */ function ButtonGroup({ children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex flex-wrap gap-3', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   InfoCard: () => (/* binding */ InfoCard),\n/* harmony export */   QuickActionCard: () => (/* binding */ QuickActionCard),\n/* harmony export */   StatusCard: () => (/* binding */ StatusCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n/**\n * Card component for organizing content in a clean, accessible way\n * Designed for easy scanning by judicial officers\n */ function Card({ children, className, padding = 'medium' }) {\n    const paddingClasses = {\n        none: 'p-0',\n        small: 'p-4',\n        medium: 'p-6',\n        large: 'p-8'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('card', paddingClasses[padding], className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Card header with optional actions\n */ function CardHeader({ children, className, actions }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('card-header', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                actions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 ml-4\",\n                    children: actions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Card content area\n */ function CardContent({ children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('space-y-4', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Card footer for actions or additional info\n */ function CardFooter({ children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('pt-4 mt-6 border-t border-gray-200', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\nfunction StatusCard({ title, status, description, statusColor = 'status-pending', icon, actions, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('hover:shadow-lg transition-shadow duration-200', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3\",\n                        children: [\n                            icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0 mt-1\",\n                                children: icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('status-badge', statusColor),\n                                            children: status\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-base\",\n                                        children: description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this),\n                    actions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 ml-4\",\n                        children: actions\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\nfunction QuickActionCard({ title, description, icon, onClick, className, disabled = false }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        disabled: disabled,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('card text-left w-full hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed', 'hover:scale-105 active:scale-95', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 p-3 bg-blue-50 rounded-lg\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-base\",\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6 text-gray-400\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 5l7 7-7 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\nfunction InfoCard({ type = 'info', title, children, className }) {\n    const typeClasses = {\n        info: 'bg-blue-50 border-blue-200 text-blue-800',\n        warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n        success: 'bg-green-50 border-green-200 text-green-800',\n        error: 'bg-red-50 border-red-200 text-red-800'\n    };\n    const iconClasses = {\n        info: 'text-blue-500',\n        warning: 'text-yellow-500',\n        success: 'text-green-500',\n        error: 'text-red-500'\n    };\n    const icons = {\n        info: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 235,\n            columnNumber: 7\n        }, this),\n        warning: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, this),\n        success: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 246,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, this),\n        error: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 251,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 250,\n            columnNumber: 7\n        }, this)\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('rounded-lg border-2 p-4', typeClasses[type], className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex-shrink-0', iconClasses[type]),\n                    children: icons[type]\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-base\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 258,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 257,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Form.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Form.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Checkbox: () => (/* binding */ Checkbox),\n/* harmony export */   FormField: () => (/* binding */ FormField),\n/* harmony export */   FormSection: () => (/* binding */ FormSection),\n/* harmony export */   Input: () => (/* binding */ Input),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup),\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n/**\n * Form field wrapper for consistent spacing and layout\n */ function FormField({ children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('space-y-2', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Form label with required indicator\n */ function Label({ required, children, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('form-label', className),\n        ...props,\n        children: [\n            children,\n            required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-red-500 ml-1\",\n                children: \"*\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 49,\n                columnNumber: 20\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Text input with error handling and help text\n */ function Input({ error, helpText, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('form-input', error && 'border-red-500 focus:border-red-500 focus:ring-red-500', className),\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-red-600 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 mr-1 flex-shrink-0\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this),\n            helpText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-gray-600\",\n                children: helpText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Textarea with error handling and help text\n */ function Textarea({ error, helpText, className, rows = 4, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                rows: rows,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('form-input resize-vertical min-h-[100px]', error && 'border-red-500 focus:border-red-500 focus:ring-red-500', className),\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-red-600 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 mr-1 flex-shrink-0\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this),\n            helpText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-gray-600\",\n                children: helpText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Select dropdown with error handling and help text\n */ function Select({ error, helpText, options, placeholder, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('form-input', error && 'border-red-500 focus:border-red-500 focus:ring-red-500', className),\n                ...props,\n                children: [\n                    placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                        value: \"\",\n                        disabled: true,\n                        children: placeholder\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this),\n                    options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                            value: option.value,\n                            disabled: option.disabled,\n                            children: option.label\n                        }, option.value, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-red-600 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 mr-1 flex-shrink-0\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this),\n            helpText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-gray-600\",\n                children: helpText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\nfunction Checkbox({ label, error, helpText, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"checkbox\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('mt-1 h-5 w-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 focus:ring-2', error && 'border-red-500', className),\n                        ...props\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-base font-medium text-gray-700 cursor-pointer\",\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            helpText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-600\",\n                                children: helpText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-red-600 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 mr-1 flex-shrink-0\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\nfunction RadioGroup({ name, options, value, onChange, error, helpText, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"radio\",\n                                id: `${name}-${option.value}`,\n                                name: name,\n                                value: option.value,\n                                checked: value === option.value,\n                                onChange: (e)=>onChange(e.target.value),\n                                disabled: option.disabled,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('mt-1 h-5 w-5 text-blue-600 border-2 border-gray-300 focus:ring-blue-500 focus:ring-2', error && 'border-red-500')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: `${name}-${option.value}`,\n                                        className: \"text-base font-medium text-gray-700 cursor-pointer\",\n                                        children: option.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    option.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-gray-600\",\n                                        children: option.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, option.value, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-red-600 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 mr-1 flex-shrink-0\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 272,\n                columnNumber: 9\n            }, this),\n            helpText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-gray-600\",\n                children: helpText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\nfunction FormSection({ title, description, children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('space-y-6', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-base text-gray-600\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Form.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Role-based permissions\nconst PERMISSIONS = {\n    magistrate: [\n        'view_all_cases',\n        'create_case',\n        'edit_case',\n        'delete_case',\n        'start_recording',\n        'draft_judgment',\n        'finalize_judgment',\n        'manage_court_list',\n        'view_analytics',\n        'manage_templates',\n        'approve_documents'\n    ],\n    clerk: [\n        'view_assigned_cases',\n        'create_case',\n        'edit_case',\n        'manage_court_list',\n        'draft_documents',\n        'prepare_templates',\n        'schedule_hearings'\n    ],\n    advocate: [\n        'view_own_cases',\n        'view_case_status',\n        'view_documents',\n        'view_schedules'\n    ]\n};\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check for existing session on mount\n            const checkExistingSession = {\n                \"AuthProvider.useEffect.checkExistingSession\": ()=>{\n                    try {\n                        const storedUser = localStorage.getItem('kesitrack_user');\n                        if (storedUser) {\n                            const userData = JSON.parse(storedUser);\n                            // Check if session is still valid (24 hours)\n                            const loginTime = new Date(userData.loginTime);\n                            const now = new Date();\n                            const hoursDiff = (now.getTime() - loginTime.getTime()) / (1000 * 60 * 60);\n                            if (hoursDiff < 24) {\n                                setUser(userData);\n                            } else {\n                                // Session expired\n                                localStorage.removeItem('kesitrack_user');\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error checking session:', error);\n                        localStorage.removeItem('kesitrack_user');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkExistingSession\"];\n            checkExistingSession();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (username, password, role)=>{\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // For demo purposes, accept any credentials\n            // In real implementation, this would validate against backend\n            const userData = {\n                username,\n                role: role,\n                loginTime: new Date().toISOString(),\n                displayName: getDisplayName(username, role)\n            };\n            localStorage.setItem('kesitrack_user', JSON.stringify(userData));\n            setUser(userData);\n            return true;\n        } catch (error) {\n            console.error('Login error:', error);\n            return false;\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem('kesitrack_user');\n        setUser(null);\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        if (!user) return false;\n        return PERMISSIONS[user.role]?.includes(permission) || false;\n    };\n    const getDisplayName = (username, role)=>{\n        const roleNames = {\n            magistrate: 'Magistrate',\n            clerk: 'Court Clerk',\n            advocate: 'Advocate'\n        };\n        // In real implementation, this would come from user database\n        return `${roleNames[role]} ${username}`;\n    };\n    const value = {\n        user,\n        login,\n        logout,\n        isLoading,\n        hasPermission\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n// Higher-order component for protecting routes\nfunction withAuth(Component, requiredPermissions) {\n    return function AuthenticatedComponent(props) {\n        const { user, isLoading, hasPermission } = useAuth();\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-10 h-10 text-white animate-spin\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"Loading KesiTrack...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Please wait while we verify your session\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            window.location.href = '/login';\n            return null;\n        }\n        // Check permissions if required\n        if (requiredPermissions && requiredPermissions.length > 0) {\n            const hasRequiredPermissions = requiredPermissions.every((permission)=>hasPermission(permission));\n            if (!hasRequiredPermissions) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-10 h-10 text-red-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                children: \"Access Denied\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"You do not have permission to access this feature. Please contact your administrator if you believe this is an error.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.history.back(),\n                                className: \"btn-secondary\",\n                                children: \"Go Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 222,\n            columnNumber: 12\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/kenya-law-reports.ts":
/*!**************************************!*\
  !*** ./src/lib/kenya-law-reports.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KENYA_LAW_CASES: () => (/* binding */ KENYA_LAW_CASES),\n/* harmony export */   KenyaLawReportsEngine: () => (/* binding */ KenyaLawReportsEngine),\n/* harmony export */   LEGAL_PRINCIPLES: () => (/* binding */ LEGAL_PRINCIPLES)\n/* harmony export */ });\n/**\n * Kenya Law Reports Integration System\n * Provides access to Kenyan case law, precedents, and legal citations\n */ /**\n * Mock Kenya Law Reports Database\n * In production, this would connect to the official Kenya Law Reports database\n */ const KENYA_LAW_CASES = [\n    {\n        id: 'klr-2023-001',\n        citation: '[2023] eKLR',\n        title: 'Republic v. John Doe',\n        court: 'High Court of Kenya at Nairobi',\n        judges: [\n            'Justice Mary Kasango',\n            'Justice Peter Kiprotich'\n        ],\n        date: '2023-03-15',\n        summary: 'Criminal case involving theft of motor vehicle. Court considered factors for bail determination under Article 49(1)(h) of the Constitution.',\n        headnotes: [\n            'Bail - Factors to consider - Flight risk, interference with witnesses, public safety',\n            'Constitutional Law - Article 49(1)(h) - Right to bail',\n            'Criminal Procedure - Section 123 Criminal Procedure Code'\n        ],\n        legalPrinciples: [\n            'Bail is a constitutional right that should not be denied arbitrarily',\n            'The court must balance the right to bail against public safety concerns',\n            'Previous convictions are relevant but not determinative for bail decisions'\n        ],\n        keywords: [\n            'bail',\n            'theft',\n            'motor vehicle',\n            'constitutional rights',\n            'flight risk'\n        ],\n        fullText: 'REPUBLIC v. JOHN DOE [2023] eKLR\\n\\nHIGH COURT OF KENYA AT NAIROBI\\n\\nCRIMINAL CASE NO. 45 OF 2023\\n\\nBefore: Justice Mary Kasango & Justice Peter Kiprotich\\n\\nDate: 15th March 2023\\n\\nJUDGMENT\\n\\nThis is an application for bail by the accused person charged with theft of a motor vehicle contrary to sections 268 and 279 of the Penal Code...',\n        category: 'criminal'\n    },\n    {\n        id: 'klr-2023-002',\n        citation: '[2023] eKLR',\n        title: 'Jane Smith v. ABC Company Limited',\n        court: 'Court of Appeal of Kenya',\n        judges: [\n            'Justice William Ouko',\n            'Justice Roselyn Nambuye',\n            'Justice Daniel Musinga'\n        ],\n        date: '2023-05-20',\n        summary: 'Employment dispute regarding wrongful termination. Court established principles for calculating compensation in employment cases.',\n        headnotes: [\n            'Employment Law - Wrongful termination - Compensation',\n            'Civil Procedure - Order 20 - Judgment within 60 days',\n            'Contract Law - Breach of employment contract'\n        ],\n        legalPrinciples: [\n            'Compensation for wrongful termination should include lost wages and benefits',\n            'Courts must deliver judgment within 60 days of close of case',\n            'Employment contracts must be interpreted to protect employee rights'\n        ],\n        keywords: [\n            'employment',\n            'wrongful termination',\n            'compensation',\n            'contract breach'\n        ],\n        fullText: 'JANE SMITH v. ABC COMPANY LIMITED [2023] eKLR\\n\\nCOURT OF APPEAL OF KENYA\\n\\nCIVIL APPEAL NO. 123 OF 2023\\n\\nBefore: Justice William Ouko, Justice Roselyn Nambuye & Justice Daniel Musinga\\n\\nDate: 20th May 2023\\n\\nJUDGMENT\\n\\nThis appeal arises from a judgment of the Employment and Labour Relations Court...',\n        category: 'civil'\n    },\n    {\n        id: 'klr-2022-003',\n        citation: '[2022] eKLR',\n        title: 'Republic v. Mary Johnson',\n        court: 'Supreme Court of Kenya',\n        judges: [\n            'Chief Justice Martha Koome',\n            'Justice Philomena Mwilu',\n            'Justice Mohamed Ibrahim'\n        ],\n        date: '2022-11-10',\n        summary: 'Constitutional interpretation of Article 50 regarding fair trial rights in criminal proceedings.',\n        headnotes: [\n            'Constitutional Law - Article 50 - Fair trial rights',\n            'Criminal Procedure - Right to legal representation',\n            'Human Rights - Due process'\n        ],\n        legalPrinciples: [\n            'Every accused person has the right to a fair trial',\n            'Legal representation is fundamental to fair trial',\n            'Courts must ensure due process is followed'\n        ],\n        keywords: [\n            'constitutional law',\n            'fair trial',\n            'legal representation',\n            'due process'\n        ],\n        fullText: 'REPUBLIC v. MARY JOHNSON [2022] eKLR\\n\\nSUPREME COURT OF KENYA\\n\\nCRIMINAL APPEAL NO. 67 OF 2022\\n\\nBefore: Chief Justice Martha Koome, Justice Philomena Mwilu & Justice Mohamed Ibrahim\\n\\nDate: 10th November 2022\\n\\nJUDGMENT\\n\\nThis appeal concerns the interpretation of Article 50 of the Constitution...',\n        category: 'constitutional'\n    },\n    {\n        id: 'klr-2023-004',\n        citation: '[2023] eKLR',\n        title: 'In the Matter of XYZ (A Minor)',\n        court: 'High Court of Kenya (Family Division)',\n        judges: [\n            'Justice Grace Nzioka'\n        ],\n        date: '2023-07-08',\n        summary: 'Child custody case establishing principles for determining the best interests of the child.',\n        headnotes: [\n            'Family Law - Child custody - Best interests of the child',\n            'Children Act - Section 4 - Welfare principle',\n            'Constitutional Law - Article 53 - Rights of children'\n        ],\n        legalPrinciples: [\n            'The best interests of the child are paramount in custody decisions',\n            'Courts must consider the child\\'s welfare above all other considerations',\n            'Both parents have equal rights unless circumstances dictate otherwise'\n        ],\n        keywords: [\n            'child custody',\n            'best interests',\n            'family law',\n            'children rights'\n        ],\n        fullText: 'IN THE MATTER OF XYZ (A MINOR) [2023] eKLR\\n\\nHIGH COURT OF KENYA (FAMILY DIVISION)\\n\\nFAMILY CASE NO. 12 OF 2023\\n\\nBefore: Justice Grace Nzioka\\n\\nDate: 8th July 2023\\n\\nJUDGMENT\\n\\nThis is an application for custody of a minor child...',\n        category: 'family'\n    },\n    {\n        id: 'klr-2023-005',\n        citation: '[2023] eKLR',\n        title: 'Kenya Commercial Bank v. Traders Limited',\n        court: 'Commercial Court',\n        judges: [\n            'Justice James Makau'\n        ],\n        date: '2023-09-12',\n        summary: 'Banking dispute regarding loan default and recovery procedures under the Banking Act.',\n        headnotes: [\n            'Banking Law - Loan default - Recovery procedures',\n            'Commercial Law - Contract enforcement',\n            'Civil Procedure - Summary judgment'\n        ],\n        legalPrinciples: [\n            'Banks must follow proper procedures before loan recovery',\n            'Commercial contracts must be enforced according to their terms',\n            'Summary judgment is appropriate in clear-cut commercial disputes'\n        ],\n        keywords: [\n            'banking',\n            'loan default',\n            'commercial law',\n            'contract enforcement'\n        ],\n        fullText: 'KENYA COMMERCIAL BANK v. TRADERS LIMITED [2023] eKLR\\n\\nCOMMERCIAL COURT\\n\\nCOMMERCIAL CASE NO. 89 OF 2023\\n\\nBefore: Justice James Makau\\n\\nDate: 12th September 2023\\n\\nJUDGMENT\\n\\nThis is a claim for recovery of a loan facility...',\n        category: 'commercial'\n    }\n];\n/**\n * Legal Principles Database\n */ const LEGAL_PRINCIPLES = [\n    {\n        id: 'principle-001',\n        principle: 'Bail is a constitutional right under Article 49(1)(h) of the Constitution',\n        source: 'Constitution of Kenya 2010',\n        citation: 'Article 49(1)(h)',\n        category: 'Constitutional Law',\n        applications: [\n            'Criminal cases',\n            'Bail applications',\n            'Human rights'\n        ]\n    },\n    {\n        id: 'principle-002',\n        principle: 'The best interests of the child are paramount in all matters concerning children',\n        source: 'Children Act 2001',\n        citation: 'Section 4',\n        category: 'Family Law',\n        applications: [\n            'Child custody',\n            'Adoption',\n            'Child protection'\n        ]\n    },\n    {\n        id: 'principle-003',\n        principle: 'Justice delayed is justice denied',\n        source: 'Common Law Principle',\n        citation: 'Various cases',\n        category: 'Procedural Law',\n        applications: [\n            'Case management',\n            'Timely delivery of judgments',\n            'Court administration'\n        ]\n    }\n];\n/**\n * Kenya Law Reports Search Engine\n */ class KenyaLawReportsEngine {\n    /**\n   * Search for cases based on keywords, legal issues, or citations\n   */ static searchCases(query, filters) {\n        const searchTerms = query.toLowerCase().split(' ');\n        let results = KENYA_LAW_CASES.filter((case_)=>{\n            // Search in title, summary, keywords, and legal principles\n            const searchableText = [\n                case_.title,\n                case_.summary,\n                ...case_.keywords,\n                ...case_.legalPrinciples,\n                ...case_.headnotes\n            ].join(' ').toLowerCase();\n            return searchTerms.some((term)=>searchableText.includes(term));\n        });\n        // Apply filters\n        if (filters?.court) {\n            results = results.filter((case_)=>case_.court.toLowerCase().includes(filters.court.toLowerCase()));\n        }\n        if (filters?.category) {\n            results = results.filter((case_)=>case_.category === filters.category);\n        }\n        if (filters?.dateFrom) {\n            results = results.filter((case_)=>case_.date >= filters.dateFrom);\n        }\n        if (filters?.dateTo) {\n            results = results.filter((case_)=>case_.date <= filters.dateTo);\n        }\n        // Calculate relevance scores\n        results = results.map((case_)=>({\n                ...case_,\n                relevanceScore: this.calculateRelevance(case_, searchTerms)\n            }));\n        // Sort by relevance\n        results.sort((a, b)=>(b.relevanceScore || 0) - (a.relevanceScore || 0));\n        // Apply limit\n        if (filters?.limit) {\n            results = results.slice(0, filters.limit);\n        }\n        return results;\n    }\n    /**\n   * Find similar cases based on legal issues and facts\n   */ static findSimilarCases(caseData) {\n        const searchQuery = [\n            ...caseData.charges || [],\n            caseData.facts,\n            ...caseData.legalIssues || []\n        ].join(' ');\n        return this.searchCases(searchQuery, {\n            category: caseData.caseType,\n            limit: 5\n        });\n    }\n    /**\n   * Get legal principles relevant to a case\n   */ static getRelevantPrinciples(keywords) {\n        return LEGAL_PRINCIPLES.filter((principle)=>keywords.some((keyword)=>principle.principle.toLowerCase().includes(keyword.toLowerCase()) || principle.applications.some((app)=>app.toLowerCase().includes(keyword.toLowerCase()))));\n    }\n    /**\n   * Format citation according to Kenya Law Reports standards\n   */ static formatCitation(case_, style = 'full') {\n        switch(style){\n            case 'full':\n                return `${case_.title} ${case_.citation}`;\n            case 'short':\n                return case_.citation;\n            case 'neutral':\n                return `${case_.citation} (${case_.court})`;\n            default:\n                return case_.citation;\n        }\n    }\n    /**\n   * Extract legal principles from case text\n   */ static extractPrinciples(caseText) {\n        // Simple principle extraction - in production, this would use NLP\n        const principles = [];\n        // Look for common legal principle indicators\n        const principleIndicators = [\n            'it is established that',\n            'the principle is',\n            'it is settled law that',\n            'the court held that',\n            'it is clear that'\n        ];\n        const sentences = caseText.split(/[.!?]+/);\n        sentences.forEach((sentence)=>{\n            const lowerSentence = sentence.toLowerCase();\n            if (principleIndicators.some((indicator)=>lowerSentence.includes(indicator))) {\n                principles.push(sentence.trim());\n            }\n        });\n        return principles.slice(0, 5) // Return top 5 principles\n        ;\n    }\n    /**\n   * Generate case summary from full text\n   */ static generateSummary(fullText, maxLength = 200) {\n        // Extract first few sentences that contain key information\n        const sentences = fullText.split(/[.!?]+/);\n        let summary = '';\n        for (const sentence of sentences){\n            if (summary.length + sentence.length > maxLength) break;\n            if (sentence.trim().length > 20) {\n                summary += sentence.trim() + '. ';\n            }\n        }\n        return summary.trim();\n    }\n    /**\n   * Calculate relevance score for search results\n   */ static calculateRelevance(case_, searchTerms) {\n        let score = 0;\n        // Title matches get highest score\n        searchTerms.forEach((term)=>{\n            if (case_.title.toLowerCase().includes(term)) score += 10;\n            if (case_.summary.toLowerCase().includes(term)) score += 5;\n            if (case_.keywords.some((keyword)=>keyword.toLowerCase().includes(term))) score += 3;\n            if (case_.legalPrinciples.some((principle)=>principle.toLowerCase().includes(term))) score += 2;\n        });\n        return score;\n    }\n    /**\n   * Get case by citation\n   */ static getCaseByCitation(citation) {\n        return KENYA_LAW_CASES.find((case_)=>case_.citation.toLowerCase() === citation.toLowerCase()) || null;\n    }\n    /**\n   * Get cases by court\n   */ static getCasesByCourt(court) {\n        return KENYA_LAW_CASES.filter((case_)=>case_.court.toLowerCase().includes(court.toLowerCase()));\n    }\n    /**\n   * Get recent cases (last 2 years)\n   */ static getRecentCases(limit = 10) {\n        const twoYearsAgo = new Date();\n        twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);\n        return KENYA_LAW_CASES.filter((case_)=>new Date(case_.date) >= twoYearsAgo).sort((a, b)=>new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, limit);\n    }\n    /**\n   * Get landmark cases by category\n   */ static getLandmarkCases(category) {\n        let cases = KENYA_LAW_CASES.filter((case_)=>case_.court.includes('Supreme Court') || case_.court.includes('Court of Appeal'));\n        if (category) {\n            cases = cases.filter((case_)=>case_.category === category);\n        }\n        return cases.sort((a, b)=>new Date(b.date).getTime() - new Date(a.date).getTime());\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/kenya-law-reports.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateBailAmount: () => (/* binding */ calculateBailAmount),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCourtTime: () => (/* binding */ formatCourtTime),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatKSh: () => (/* binding */ formatKSh),\n/* harmony export */   formatLegalDate: () => (/* binding */ formatLegalDate),\n/* harmony export */   generateCaseNumber: () => (/* binding */ generateCaseNumber),\n/* harmony export */   getCaseStatusColor: () => (/* binding */ getCaseStatusColor),\n/* harmony export */   getUserFriendlyError: () => (/* binding */ getUserFriendlyError),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   scrollToElement: () => (/* binding */ scrollToElement),\n/* harmony export */   simpleSearch: () => (/* binding */ simpleSearch),\n/* harmony export */   validateKenyanID: () => (/* binding */ validateKenyanID),\n/* harmony export */   validateKenyanPhone: () => (/* binding */ validateKenyanPhone)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Utility function to merge Tailwind CSS classes\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format currency in Kenya Shillings\n */ function formatKSh(amount) {\n    return new Intl.NumberFormat('en-KE', {\n        style: 'currency',\n        currency: 'KES',\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).format(amount);\n}\n/**\n * Format date for Kenyan legal system\n */ function formatLegalDate(date) {\n    return new Intl.DateTimeFormat('en-KE', {\n        day: 'numeric',\n        month: 'long',\n        year: 'numeric'\n    }).format(date);\n}\n/**\n * Format time for court proceedings\n */ function formatCourtTime(date) {\n    return new Intl.DateTimeFormat('en-KE', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n    }).format(date);\n}\n/**\n * Generate case number format\n */ function generateCaseNumber(type, year, sequence, court = 'MC') {\n    const typeCode = {\n        CRIMINAL: 'CR',\n        CIVIL: 'CV',\n        FAMILY: 'FM',\n        COMMERCIAL: 'CM'\n    }[type];\n    return `${typeCode} ${sequence}/${year} ${court}`;\n}\n/**\n * Validate Kenya ID number\n */ function validateKenyanID(id) {\n    const idRegex = /^\\d{8}$/;\n    return idRegex.test(id);\n}\n/**\n * Validate Kenya phone number\n */ function validateKenyanPhone(phone) {\n    const phoneRegex = /^(\\+254|0)[17]\\d{8}$/;\n    return phoneRegex.test(phone);\n}\n/**\n * Get case status color for UI\n */ function getCaseStatusColor(status) {\n    const statusColors = {\n        'PENDING': 'status-pending',\n        'ACTIVE': 'status-active',\n        'HEARING': 'status-active',\n        'JUDGMENT': 'status-active',\n        'COMPLETED': 'status-completed',\n        'DISMISSED': 'status-cancelled',\n        'WITHDRAWN': 'status-cancelled'\n    };\n    return statusColors[status] || 'status-pending';\n}\n/**\n * Calculate bail amount based on offense type (simplified)\n */ function calculateBailAmount(offenseType, offenseGravity) {\n    const baseAmounts = {\n        'THEFT': {\n            LOW: 10000,\n            MEDIUM: 25000,\n            HIGH: 50000\n        },\n        'ASSAULT': {\n            LOW: 15000,\n            MEDIUM: 30000,\n            HIGH: 75000\n        },\n        'FRAUD': {\n            LOW: 50000,\n            MEDIUM: 100000,\n            HIGH: 200000\n        },\n        'DRUG_OFFENSE': {\n            LOW: 20000,\n            MEDIUM: 50000,\n            HIGH: 100000\n        },\n        'TRAFFIC': {\n            LOW: 5000,\n            MEDIUM: 15000,\n            HIGH: 30000\n        },\n        'DEFAULT': {\n            LOW: 10000,\n            MEDIUM: 25000,\n            HIGH: 50000\n        }\n    };\n    const amounts = baseAmounts[offenseType] || baseAmounts.DEFAULT;\n    return amounts[offenseGravity];\n}\n/**\n * Get user-friendly error messages\n */ function getUserFriendlyError(error) {\n    const errorMessages = {\n        'NETWORK_ERROR': 'Please check your internet connection and try again.',\n        'VALIDATION_ERROR': 'Please check the information you entered and try again.',\n        'PERMISSION_ERROR': 'You do not have permission to perform this action.',\n        'NOT_FOUND': 'The requested information could not be found.',\n        'SERVER_ERROR': 'There was a problem with the system. Please try again later.',\n        'TIMEOUT': 'The request took too long. Please try again.'\n    };\n    return errorMessages[error] || 'An unexpected error occurred. Please try again.';\n}\n/**\n * Debounce function for search inputs\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Simple text search function\n */ function simpleSearch(items, searchTerm, searchFields) {\n    if (!searchTerm.trim()) return items;\n    const term = searchTerm.toLowerCase();\n    return items.filter((item)=>searchFields.some((field)=>{\n            const value = getNestedValue(item, field);\n            return value && value.toString().toLowerCase().includes(term);\n        }));\n}\n/**\n * Get nested object value by path\n */ function getNestedValue(obj, path) {\n    return path.split('.').reduce((current, key)=>current?.[key], obj);\n}\n/**\n * Format file size for display\n */ function formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = [\n        'Bytes',\n        'KB',\n        'MB',\n        'GB'\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n/**\n * Check if user is on mobile device\n */ function isMobile() {\n    if (true) return false;\n    return window.innerWidth < 768;\n}\n/**\n * Scroll to element smoothly\n */ function scrollToElement(elementId) {\n    const element = document.getElementById(elementId);\n    if (element) {\n        element.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcase-law%2Fpage&page=%2Fcase-law%2Fpage&appPaths=%2Fcase-law%2Fpage&pagePath=private-next-app-dir%2Fcase-law%2Fpage.tsx&appDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();