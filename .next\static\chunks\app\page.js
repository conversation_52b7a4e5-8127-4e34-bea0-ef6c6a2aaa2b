/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs":
/*!*****************************************!*\
  !*** ./node_modules/clsx/dist/clsx.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clsx: () => (/* binding */ clsx),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clsx);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9jbHN4L2Rpc3QvY2xzeC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxjQUFjLGFBQWEsK0NBQStDLGdEQUFnRCxlQUFlLFFBQVEsSUFBSSwwQ0FBMEMseUNBQXlDLFNBQWdCLGdCQUFnQix3Q0FBd0MsSUFBSSxtREFBbUQsU0FBUyxpRUFBZSxJQUFJIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhcmNoXFxEZXNrdG9wXFxLZXNpVHJhY2tcXG5vZGVfbW9kdWxlc1xcY2xzeFxcZGlzdFxcY2xzeC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gcihlKXt2YXIgdCxmLG49XCJcIjtpZihcInN0cmluZ1wiPT10eXBlb2YgZXx8XCJudW1iZXJcIj09dHlwZW9mIGUpbis9ZTtlbHNlIGlmKFwib2JqZWN0XCI9PXR5cGVvZiBlKWlmKEFycmF5LmlzQXJyYXkoZSkpe3ZhciBvPWUubGVuZ3RoO2Zvcih0PTA7dDxvO3QrKyllW3RdJiYoZj1yKGVbdF0pKSYmKG4mJihuKz1cIiBcIiksbis9Zil9ZWxzZSBmb3IoZiBpbiBlKWVbZl0mJihuJiYobis9XCIgXCIpLG4rPWYpO3JldHVybiBufWV4cG9ydCBmdW5jdGlvbiBjbHN4KCl7Zm9yKHZhciBlLHQsZj0wLG49XCJcIixvPWFyZ3VtZW50cy5sZW5ndGg7ZjxvO2YrKykoZT1hcmd1bWVudHNbZl0pJiYodD1yKGUpKSYmKG4mJihuKz1cIiBcIiksbis9dCk7cmV0dXJuIG59ZXhwb3J0IGRlZmF1bHQgY2xzeDsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDc2FyY2glNUMlNUNEZXNrdG9wJTVDJTVDS2VzaVRyYWNrJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4SkFBNkYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHNhcmNoXFxcXERlc2t0b3BcXFxcS2VzaVRyYWNrXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FyY2hcXERlc2t0b3BcXEtlc2lUcmFja1xcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/tailwind-merge/dist/bundle-mjs.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTailwindMerge: () => (/* binding */ createTailwindMerge),\n/* harmony export */   extendTailwindMerge: () => (/* binding */ extendTailwindMerge),\n/* harmony export */   fromTheme: () => (/* binding */ fromTheme),\n/* harmony export */   getDefaultConfig: () => (/* binding */ getDefaultConfig),\n/* harmony export */   mergeConfigs: () => (/* binding */ mergeConfigs),\n/* harmony export */   twJoin: () => (/* binding */ twJoin),\n/* harmony export */   twMerge: () => (/* binding */ twMerge),\n/* harmony export */   validators: () => (/* binding */ validators)\n/* harmony export */ });\nconst CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = config => {\n  const classMap = createClassMap(config);\n  const {\n    conflictingClassGroups,\n    conflictingClassGroupModifiers\n  } = config;\n  const getClassGroupId = className => {\n    const classParts = className.split(CLASS_PART_SEPARATOR);\n    // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n    if (classParts[0] === '' && classParts.length !== 1) {\n      classParts.shift();\n    }\n    return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n  };\n  const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier) => {\n    const conflicts = conflictingClassGroups[classGroupId] || [];\n    if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n      return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]];\n    }\n    return conflicts;\n  };\n  return {\n    getClassGroupId,\n    getConflictingClassGroupIds\n  };\n};\nconst getGroupRecursive = (classParts, classPartObject) => {\n  if (classParts.length === 0) {\n    return classPartObject.classGroupId;\n  }\n  const currentClassPart = classParts[0];\n  const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n  const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n  if (classGroupFromNextClassPart) {\n    return classGroupFromNextClassPart;\n  }\n  if (classPartObject.validators.length === 0) {\n    return undefined;\n  }\n  const classRest = classParts.join(CLASS_PART_SEPARATOR);\n  return classPartObject.validators.find(({\n    validator\n  }) => validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = className => {\n  if (arbitraryPropertyRegex.test(className)) {\n    const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n    const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(':'));\n    if (property) {\n      // I use two dots here because one dot is used as prefix for class groups in plugins\n      return 'arbitrary..' + property;\n    }\n  }\n};\n/**\n * Exported for testing only\n */\nconst createClassMap = config => {\n  const {\n    theme,\n    classGroups\n  } = config;\n  const classMap = {\n    nextPart: new Map(),\n    validators: []\n  };\n  for (const classGroupId in classGroups) {\n    processClassesRecursively(classGroups[classGroupId], classMap, classGroupId, theme);\n  }\n  return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme) => {\n  classGroup.forEach(classDefinition => {\n    if (typeof classDefinition === 'string') {\n      const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n      classPartObjectToEdit.classGroupId = classGroupId;\n      return;\n    }\n    if (typeof classDefinition === 'function') {\n      if (isThemeGetter(classDefinition)) {\n        processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n        return;\n      }\n      classPartObject.validators.push({\n        validator: classDefinition,\n        classGroupId\n      });\n      return;\n    }\n    Object.entries(classDefinition).forEach(([key, classGroup]) => {\n      processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n    });\n  });\n};\nconst getPart = (classPartObject, path) => {\n  let currentClassPartObject = classPartObject;\n  path.split(CLASS_PART_SEPARATOR).forEach(pathPart => {\n    if (!currentClassPartObject.nextPart.has(pathPart)) {\n      currentClassPartObject.nextPart.set(pathPart, {\n        nextPart: new Map(),\n        validators: []\n      });\n    }\n    currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n  });\n  return currentClassPartObject;\n};\nconst isThemeGetter = func => func.isThemeGetter;\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = maxCacheSize => {\n  if (maxCacheSize < 1) {\n    return {\n      get: () => undefined,\n      set: () => {}\n    };\n  }\n  let cacheSize = 0;\n  let cache = new Map();\n  let previousCache = new Map();\n  const update = (key, value) => {\n    cache.set(key, value);\n    cacheSize++;\n    if (cacheSize > maxCacheSize) {\n      cacheSize = 0;\n      previousCache = cache;\n      cache = new Map();\n    }\n  };\n  return {\n    get(key) {\n      let value = cache.get(key);\n      if (value !== undefined) {\n        return value;\n      }\n      if ((value = previousCache.get(key)) !== undefined) {\n        update(key, value);\n        return value;\n      }\n    },\n    set(key, value) {\n      if (cache.has(key)) {\n        cache.set(key, value);\n      } else {\n        update(key, value);\n      }\n    }\n  };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst MODIFIER_SEPARATOR = ':';\nconst MODIFIER_SEPARATOR_LENGTH = MODIFIER_SEPARATOR.length;\nconst createParseClassName = config => {\n  const {\n    prefix,\n    experimentalParseClassName\n  } = config;\n  /**\n   * Parse class name into parts.\n   *\n   * Inspired by `splitAtTopLevelOnly` used in Tailwind CSS\n   * @see https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n   */\n  let parseClassName = className => {\n    const modifiers = [];\n    let bracketDepth = 0;\n    let parenDepth = 0;\n    let modifierStart = 0;\n    let postfixModifierPosition;\n    for (let index = 0; index < className.length; index++) {\n      let currentCharacter = className[index];\n      if (bracketDepth === 0 && parenDepth === 0) {\n        if (currentCharacter === MODIFIER_SEPARATOR) {\n          modifiers.push(className.slice(modifierStart, index));\n          modifierStart = index + MODIFIER_SEPARATOR_LENGTH;\n          continue;\n        }\n        if (currentCharacter === '/') {\n          postfixModifierPosition = index;\n          continue;\n        }\n      }\n      if (currentCharacter === '[') {\n        bracketDepth++;\n      } else if (currentCharacter === ']') {\n        bracketDepth--;\n      } else if (currentCharacter === '(') {\n        parenDepth++;\n      } else if (currentCharacter === ')') {\n        parenDepth--;\n      }\n    }\n    const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n    const baseClassName = stripImportantModifier(baseClassNameWithImportantModifier);\n    const hasImportantModifier = baseClassName !== baseClassNameWithImportantModifier;\n    const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n    return {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    };\n  };\n  if (prefix) {\n    const fullPrefix = prefix + MODIFIER_SEPARATOR;\n    const parseClassNameOriginal = parseClassName;\n    parseClassName = className => className.startsWith(fullPrefix) ? parseClassNameOriginal(className.substring(fullPrefix.length)) : {\n      isExternal: true,\n      modifiers: [],\n      hasImportantModifier: false,\n      baseClassName: className,\n      maybePostfixModifierPosition: undefined\n    };\n  }\n  if (experimentalParseClassName) {\n    const parseClassNameOriginal = parseClassName;\n    parseClassName = className => experimentalParseClassName({\n      className,\n      parseClassName: parseClassNameOriginal\n    });\n  }\n  return parseClassName;\n};\nconst stripImportantModifier = baseClassName => {\n  if (baseClassName.endsWith(IMPORTANT_MODIFIER)) {\n    return baseClassName.substring(0, baseClassName.length - 1);\n  }\n  /**\n   * In Tailwind CSS v3 the important modifier was at the start of the base class name. This is still supported for legacy reasons.\n   * @see https://github.com/dcastil/tailwind-merge/issues/513#issuecomment-2614029864\n   */\n  if (baseClassName.startsWith(IMPORTANT_MODIFIER)) {\n    return baseClassName.substring(1);\n  }\n  return baseClassName;\n};\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nconst createSortModifiers = config => {\n  const orderSensitiveModifiers = Object.fromEntries(config.orderSensitiveModifiers.map(modifier => [modifier, true]));\n  const sortModifiers = modifiers => {\n    if (modifiers.length <= 1) {\n      return modifiers;\n    }\n    const sortedModifiers = [];\n    let unsortedModifiers = [];\n    modifiers.forEach(modifier => {\n      const isPositionSensitive = modifier[0] === '[' || orderSensitiveModifiers[modifier];\n      if (isPositionSensitive) {\n        sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n        unsortedModifiers = [];\n      } else {\n        unsortedModifiers.push(modifier);\n      }\n    });\n    sortedModifiers.push(...unsortedModifiers.sort());\n    return sortedModifiers;\n  };\n  return sortModifiers;\n};\nconst createConfigUtils = config => ({\n  cache: createLruCache(config.cacheSize),\n  parseClassName: createParseClassName(config),\n  sortModifiers: createSortModifiers(config),\n  ...createClassGroupUtils(config)\n});\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils) => {\n  const {\n    parseClassName,\n    getClassGroupId,\n    getConflictingClassGroupIds,\n    sortModifiers\n  } = configUtils;\n  /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */\n  const classGroupsInConflict = [];\n  const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n  let result = '';\n  for (let index = classNames.length - 1; index >= 0; index -= 1) {\n    const originalClassName = classNames[index];\n    const {\n      isExternal,\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    } = parseClassName(originalClassName);\n    if (isExternal) {\n      result = originalClassName + (result.length > 0 ? ' ' + result : result);\n      continue;\n    }\n    let hasPostfixModifier = !!maybePostfixModifierPosition;\n    let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n    if (!classGroupId) {\n      if (!hasPostfixModifier) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      classGroupId = getClassGroupId(baseClassName);\n      if (!classGroupId) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      hasPostfixModifier = false;\n    }\n    const variantModifier = sortModifiers(modifiers).join(':');\n    const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n    const classId = modifierId + classGroupId;\n    if (classGroupsInConflict.includes(classId)) {\n      // Tailwind class omitted due to conflict\n      continue;\n    }\n    classGroupsInConflict.push(classId);\n    const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n    for (let i = 0; i < conflictGroups.length; ++i) {\n      const group = conflictGroups[i];\n      classGroupsInConflict.push(modifierId + group);\n    }\n    // Tailwind class not in conflict\n    result = originalClassName + (result.length > 0 ? ' ' + result : result);\n  }\n  return result;\n};\n\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */\nfunction twJoin() {\n  let index = 0;\n  let argument;\n  let resolvedValue;\n  let string = '';\n  while (index < arguments.length) {\n    if (argument = arguments[index++]) {\n      if (resolvedValue = toValue(argument)) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nconst toValue = mix => {\n  if (typeof mix === 'string') {\n    return mix;\n  }\n  let resolvedValue;\n  let string = '';\n  for (let k = 0; k < mix.length; k++) {\n    if (mix[k]) {\n      if (resolvedValue = toValue(mix[k])) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n  let configUtils;\n  let cacheGet;\n  let cacheSet;\n  let functionToCall = initTailwindMerge;\n  function initTailwindMerge(classList) {\n    const config = createConfigRest.reduce((previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig), createConfigFirst());\n    configUtils = createConfigUtils(config);\n    cacheGet = configUtils.cache.get;\n    cacheSet = configUtils.cache.set;\n    functionToCall = tailwindMerge;\n    return tailwindMerge(classList);\n  }\n  function tailwindMerge(classList) {\n    const cachedResult = cacheGet(classList);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    const result = mergeClassList(classList, configUtils);\n    cacheSet(classList, result);\n    return result;\n  }\n  return function callTailwindMerge() {\n    return functionToCall(twJoin.apply(null, arguments));\n  };\n}\nconst fromTheme = key => {\n  const themeGetter = theme => theme[key] || [];\n  themeGetter.isThemeGetter = true;\n  return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:(\\w[\\w-]*):)?(.+)\\]$/i;\nconst arbitraryVariableRegex = /^\\((?:(\\w[\\w-]*):)?(.+)\\)$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isFraction = value => fractionRegex.test(value);\nconst isNumber = value => !!value && !Number.isNaN(Number(value));\nconst isInteger = value => !!value && Number.isInteger(Number(value));\nconst isPercent = value => value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isTshirtSize = value => tshirtUnitRegex.test(value);\nconst isAny = () => true;\nconst isLengthOnly = value =>\n// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n// For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n// I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\nlengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = () => false;\nconst isShadow = value => shadowRegex.test(value);\nconst isImage = value => imageRegex.test(value);\nconst isAnyNonArbitrary = value => !isArbitraryValue(value) && !isArbitraryVariable(value);\nconst isArbitrarySize = value => getIsArbitraryValue(value, isLabelSize, isNever);\nconst isArbitraryValue = value => arbitraryValueRegex.test(value);\nconst isArbitraryLength = value => getIsArbitraryValue(value, isLabelLength, isLengthOnly);\nconst isArbitraryNumber = value => getIsArbitraryValue(value, isLabelNumber, isNumber);\nconst isArbitraryPosition = value => getIsArbitraryValue(value, isLabelPosition, isNever);\nconst isArbitraryImage = value => getIsArbitraryValue(value, isLabelImage, isImage);\nconst isArbitraryShadow = value => getIsArbitraryValue(value, isLabelShadow, isShadow);\nconst isArbitraryVariable = value => arbitraryVariableRegex.test(value);\nconst isArbitraryVariableLength = value => getIsArbitraryVariable(value, isLabelLength);\nconst isArbitraryVariableFamilyName = value => getIsArbitraryVariable(value, isLabelFamilyName);\nconst isArbitraryVariablePosition = value => getIsArbitraryVariable(value, isLabelPosition);\nconst isArbitraryVariableSize = value => getIsArbitraryVariable(value, isLabelSize);\nconst isArbitraryVariableImage = value => getIsArbitraryVariable(value, isLabelImage);\nconst isArbitraryVariableShadow = value => getIsArbitraryVariable(value, isLabelShadow, true);\n// Helpers\nconst getIsArbitraryValue = (value, testLabel, testValue) => {\n  const result = arbitraryValueRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return testLabel(result[1]);\n    }\n    return testValue(result[2]);\n  }\n  return false;\n};\nconst getIsArbitraryVariable = (value, testLabel, shouldMatchNoLabel = false) => {\n  const result = arbitraryVariableRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return testLabel(result[1]);\n    }\n    return shouldMatchNoLabel;\n  }\n  return false;\n};\n// Labels\nconst isLabelPosition = label => label === 'position' || label === 'percentage';\nconst isLabelImage = label => label === 'image' || label === 'url';\nconst isLabelSize = label => label === 'length' || label === 'size' || label === 'bg-size';\nconst isLabelLength = label => label === 'length';\nconst isLabelNumber = label => label === 'number';\nconst isLabelFamilyName = label => label === 'family-name';\nconst isLabelShadow = label => label === 'shadow';\nconst validators = /*#__PURE__*/Object.defineProperty({\n  __proto__: null,\n  isAny,\n  isAnyNonArbitrary,\n  isArbitraryImage,\n  isArbitraryLength,\n  isArbitraryNumber,\n  isArbitraryPosition,\n  isArbitraryShadow,\n  isArbitrarySize,\n  isArbitraryValue,\n  isArbitraryVariable,\n  isArbitraryVariableFamilyName,\n  isArbitraryVariableImage,\n  isArbitraryVariableLength,\n  isArbitraryVariablePosition,\n  isArbitraryVariableShadow,\n  isArbitraryVariableSize,\n  isFraction,\n  isInteger,\n  isNumber,\n  isPercent,\n  isTshirtSize\n}, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst getDefaultConfig = () => {\n  /**\n   * Theme getters for theme variable namespaces\n   * @see https://tailwindcss.com/docs/theme#theme-variable-namespaces\n   */\n  /***/\n  const themeColor = fromTheme('color');\n  const themeFont = fromTheme('font');\n  const themeText = fromTheme('text');\n  const themeFontWeight = fromTheme('font-weight');\n  const themeTracking = fromTheme('tracking');\n  const themeLeading = fromTheme('leading');\n  const themeBreakpoint = fromTheme('breakpoint');\n  const themeContainer = fromTheme('container');\n  const themeSpacing = fromTheme('spacing');\n  const themeRadius = fromTheme('radius');\n  const themeShadow = fromTheme('shadow');\n  const themeInsetShadow = fromTheme('inset-shadow');\n  const themeTextShadow = fromTheme('text-shadow');\n  const themeDropShadow = fromTheme('drop-shadow');\n  const themeBlur = fromTheme('blur');\n  const themePerspective = fromTheme('perspective');\n  const themeAspect = fromTheme('aspect');\n  const themeEase = fromTheme('ease');\n  const themeAnimate = fromTheme('animate');\n  /**\n   * Helpers to avoid repeating the same scales\n   *\n   * We use functions that create a new array every time they're called instead of static arrays.\n   * This ensures that users who modify any scale by mutating the array (e.g. with `array.push(element)`) don't accidentally mutate arrays in other parts of the config.\n   */\n  /***/\n  const scaleBreak = () => ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'];\n  const scalePosition = () => ['center', 'top', 'bottom', 'left', 'right', 'top-left',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'left-top', 'top-right',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'right-top', 'bottom-right',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'right-bottom', 'bottom-left',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'left-bottom'];\n  const scalePositionWithArbitrary = () => [...scalePosition(), isArbitraryVariable, isArbitraryValue];\n  const scaleOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'];\n  const scaleOverscroll = () => ['auto', 'contain', 'none'];\n  const scaleUnambiguousSpacing = () => [isArbitraryVariable, isArbitraryValue, themeSpacing];\n  const scaleInset = () => [isFraction, 'full', 'auto', ...scaleUnambiguousSpacing()];\n  const scaleGridTemplateColsRows = () => [isInteger, 'none', 'subgrid', isArbitraryVariable, isArbitraryValue];\n  const scaleGridColRowStartAndEnd = () => ['auto', {\n    span: ['full', isInteger, isArbitraryVariable, isArbitraryValue]\n  }, isInteger, isArbitraryVariable, isArbitraryValue];\n  const scaleGridColRowStartOrEnd = () => [isInteger, 'auto', isArbitraryVariable, isArbitraryValue];\n  const scaleGridAutoColsRows = () => ['auto', 'min', 'max', 'fr', isArbitraryVariable, isArbitraryValue];\n  const scaleAlignPrimaryAxis = () => ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch', 'baseline', 'center-safe', 'end-safe'];\n  const scaleAlignSecondaryAxis = () => ['start', 'end', 'center', 'stretch', 'center-safe', 'end-safe'];\n  const scaleMargin = () => ['auto', ...scaleUnambiguousSpacing()];\n  const scaleSizing = () => [isFraction, 'auto', 'full', 'dvw', 'dvh', 'lvw', 'lvh', 'svw', 'svh', 'min', 'max', 'fit', ...scaleUnambiguousSpacing()];\n  const scaleColor = () => [themeColor, isArbitraryVariable, isArbitraryValue];\n  const scaleBgPosition = () => [...scalePosition(), isArbitraryVariablePosition, isArbitraryPosition, {\n    position: [isArbitraryVariable, isArbitraryValue]\n  }];\n  const scaleBgRepeat = () => ['no-repeat', {\n    repeat: ['', 'x', 'y', 'space', 'round']\n  }];\n  const scaleBgSize = () => ['auto', 'cover', 'contain', isArbitraryVariableSize, isArbitrarySize, {\n    size: [isArbitraryVariable, isArbitraryValue]\n  }];\n  const scaleGradientStopPosition = () => [isPercent, isArbitraryVariableLength, isArbitraryLength];\n  const scaleRadius = () => [\n  // Deprecated since Tailwind CSS v4.0.0\n  '', 'none', 'full', themeRadius, isArbitraryVariable, isArbitraryValue];\n  const scaleBorderWidth = () => ['', isNumber, isArbitraryVariableLength, isArbitraryLength];\n  const scaleLineStyle = () => ['solid', 'dashed', 'dotted', 'double'];\n  const scaleBlendMode = () => ['normal', 'multiply', 'screen', 'overlay', 'darken', 'lighten', 'color-dodge', 'color-burn', 'hard-light', 'soft-light', 'difference', 'exclusion', 'hue', 'saturation', 'color', 'luminosity'];\n  const scaleMaskImagePosition = () => [isNumber, isPercent, isArbitraryVariablePosition, isArbitraryPosition];\n  const scaleBlur = () => [\n  // Deprecated since Tailwind CSS v4.0.0\n  '', 'none', themeBlur, isArbitraryVariable, isArbitraryValue];\n  const scaleRotate = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleScale = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleSkew = () => [isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleTranslate = () => [isFraction, 'full', ...scaleUnambiguousSpacing()];\n  return {\n    cacheSize: 500,\n    theme: {\n      animate: ['spin', 'ping', 'pulse', 'bounce'],\n      aspect: ['video'],\n      blur: [isTshirtSize],\n      breakpoint: [isTshirtSize],\n      color: [isAny],\n      container: [isTshirtSize],\n      'drop-shadow': [isTshirtSize],\n      ease: ['in', 'out', 'in-out'],\n      font: [isAnyNonArbitrary],\n      'font-weight': ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black'],\n      'inset-shadow': [isTshirtSize],\n      leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose'],\n      perspective: ['dramatic', 'near', 'normal', 'midrange', 'distant', 'none'],\n      radius: [isTshirtSize],\n      shadow: [isTshirtSize],\n      spacing: ['px', isNumber],\n      text: [isTshirtSize],\n      'text-shadow': [isTshirtSize],\n      tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest']\n    },\n    classGroups: {\n      // --------------\n      // --- Layout ---\n      // --------------\n      /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */\n      aspect: [{\n        aspect: ['auto', 'square', isFraction, isArbitraryValue, isArbitraryVariable, themeAspect]\n      }],\n      /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       * @deprecated since Tailwind CSS v4.0.0\n       */\n      container: ['container'],\n      /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */\n      columns: [{\n        columns: [isNumber, isArbitraryValue, isArbitraryVariable, themeContainer]\n      }],\n      /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */\n      'break-after': [{\n        'break-after': scaleBreak()\n      }],\n      /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */\n      'break-before': [{\n        'break-before': scaleBreak()\n      }],\n      /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */\n      'break-inside': [{\n        'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column']\n      }],\n      /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */\n      'box-decoration': [{\n        'box-decoration': ['slice', 'clone']\n      }],\n      /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */\n      box: [{\n        box: ['border', 'content']\n      }],\n      /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */\n      display: ['block', 'inline-block', 'inline', 'flex', 'inline-flex', 'table', 'inline-table', 'table-caption', 'table-cell', 'table-column', 'table-column-group', 'table-footer-group', 'table-header-group', 'table-row-group', 'table-row', 'flow-root', 'grid', 'inline-grid', 'contents', 'list-item', 'hidden'],\n      /**\n       * Screen Reader Only\n       * @see https://tailwindcss.com/docs/display#screen-reader-only\n       */\n      sr: ['sr-only', 'not-sr-only'],\n      /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */\n      float: [{\n        float: ['right', 'left', 'none', 'start', 'end']\n      }],\n      /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */\n      clear: [{\n        clear: ['left', 'right', 'both', 'none', 'start', 'end']\n      }],\n      /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */\n      isolation: ['isolate', 'isolation-auto'],\n      /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */\n      'object-fit': [{\n        object: ['contain', 'cover', 'fill', 'none', 'scale-down']\n      }],\n      /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */\n      'object-position': [{\n        object: scalePositionWithArbitrary()\n      }],\n      /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      overflow: [{\n        overflow: scaleOverflow()\n      }],\n      /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-x': [{\n        'overflow-x': scaleOverflow()\n      }],\n      /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-y': [{\n        'overflow-y': scaleOverflow()\n      }],\n      /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      overscroll: [{\n        overscroll: scaleOverscroll()\n      }],\n      /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-x': [{\n        'overscroll-x': scaleOverscroll()\n      }],\n      /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-y': [{\n        'overscroll-y': scaleOverscroll()\n      }],\n      /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */\n      position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n      /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      inset: [{\n        inset: scaleInset()\n      }],\n      /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-x': [{\n        'inset-x': scaleInset()\n      }],\n      /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-y': [{\n        'inset-y': scaleInset()\n      }],\n      /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      start: [{\n        start: scaleInset()\n      }],\n      /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      end: [{\n        end: scaleInset()\n      }],\n      /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      top: [{\n        top: scaleInset()\n      }],\n      /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      right: [{\n        right: scaleInset()\n      }],\n      /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      bottom: [{\n        bottom: scaleInset()\n      }],\n      /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      left: [{\n        left: scaleInset()\n      }],\n      /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */\n      visibility: ['visible', 'invisible', 'collapse'],\n      /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */\n      z: [{\n        z: [isInteger, 'auto', isArbitraryVariable, isArbitraryValue]\n      }],\n      // ------------------------\n      // --- Flexbox and Grid ---\n      // ------------------------\n      /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */\n      basis: [{\n        basis: [isFraction, 'full', 'auto', themeContainer, ...scaleUnambiguousSpacing()]\n      }],\n      /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */\n      'flex-direction': [{\n        flex: ['row', 'row-reverse', 'col', 'col-reverse']\n      }],\n      /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */\n      'flex-wrap': [{\n        flex: ['nowrap', 'wrap', 'wrap-reverse']\n      }],\n      /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */\n      flex: [{\n        flex: [isNumber, isFraction, 'auto', 'initial', 'none', isArbitraryValue]\n      }],\n      /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */\n      grow: [{\n        grow: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */\n      shrink: [{\n        shrink: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */\n      order: [{\n        order: [isInteger, 'first', 'last', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */\n      'grid-cols': [{\n        'grid-cols': scaleGridTemplateColsRows()\n      }],\n      /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start-end': [{\n        col: scaleGridColRowStartAndEnd()\n      }],\n      /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start': [{\n        'col-start': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-end': [{\n        'col-end': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */\n      'grid-rows': [{\n        'grid-rows': scaleGridTemplateColsRows()\n      }],\n      /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start-end': [{\n        row: scaleGridColRowStartAndEnd()\n      }],\n      /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start': [{\n        'row-start': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-end': [{\n        'row-end': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */\n      'grid-flow': [{\n        'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense']\n      }],\n      /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */\n      'auto-cols': [{\n        'auto-cols': scaleGridAutoColsRows()\n      }],\n      /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */\n      'auto-rows': [{\n        'auto-rows': scaleGridAutoColsRows()\n      }],\n      /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */\n      gap: [{\n        gap: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-x': [{\n        'gap-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-y': [{\n        'gap-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */\n      'justify-content': [{\n        justify: [...scaleAlignPrimaryAxis(), 'normal']\n      }],\n      /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */\n      'justify-items': [{\n        'justify-items': [...scaleAlignSecondaryAxis(), 'normal']\n      }],\n      /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */\n      'justify-self': [{\n        'justify-self': ['auto', ...scaleAlignSecondaryAxis()]\n      }],\n      /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */\n      'align-content': [{\n        content: ['normal', ...scaleAlignPrimaryAxis()]\n      }],\n      /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */\n      'align-items': [{\n        items: [...scaleAlignSecondaryAxis(), {\n          baseline: ['', 'last']\n        }]\n      }],\n      /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */\n      'align-self': [{\n        self: ['auto', ...scaleAlignSecondaryAxis(), {\n          baseline: ['', 'last']\n        }]\n      }],\n      /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */\n      'place-content': [{\n        'place-content': scaleAlignPrimaryAxis()\n      }],\n      /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */\n      'place-items': [{\n        'place-items': [...scaleAlignSecondaryAxis(), 'baseline']\n      }],\n      /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */\n      'place-self': [{\n        'place-self': ['auto', ...scaleAlignSecondaryAxis()]\n      }],\n      // Spacing\n      /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */\n      p: [{\n        p: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */\n      px: [{\n        px: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */\n      py: [{\n        py: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */\n      ps: [{\n        ps: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pe: [{\n        pe: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pt: [{\n        pt: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pr: [{\n        pr: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pb: [{\n        pb: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pl: [{\n        pl: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */\n      m: [{\n        m: scaleMargin()\n      }],\n      /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mx: [{\n        mx: scaleMargin()\n      }],\n      /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */\n      my: [{\n        my: scaleMargin()\n      }],\n      /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ms: [{\n        ms: scaleMargin()\n      }],\n      /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */\n      me: [{\n        me: scaleMargin()\n      }],\n      /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mt: [{\n        mt: scaleMargin()\n      }],\n      /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mr: [{\n        mr: scaleMargin()\n      }],\n      /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mb: [{\n        mb: scaleMargin()\n      }],\n      /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ml: [{\n        ml: scaleMargin()\n      }],\n      /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-x': [{\n        'space-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-x-reverse': ['space-x-reverse'],\n      /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-y': [{\n        'space-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-y-reverse': ['space-y-reverse'],\n      // --------------\n      // --- Sizing ---\n      // --------------\n      /**\n       * Size\n       * @see https://tailwindcss.com/docs/width#setting-both-width-and-height\n       */\n      size: [{\n        size: scaleSizing()\n      }],\n      /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */\n      w: [{\n        w: [themeContainer, 'screen', ...scaleSizing()]\n      }],\n      /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */\n      'min-w': [{\n        'min-w': [themeContainer, 'screen', /** Deprecated. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        'none', ...scaleSizing()]\n      }],\n      /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */\n      'max-w': [{\n        'max-w': [themeContainer, 'screen', 'none', /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        'prose', /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        {\n          screen: [themeBreakpoint]\n        }, ...scaleSizing()]\n      }],\n      /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */\n      h: [{\n        h: ['screen', 'lh', ...scaleSizing()]\n      }],\n      /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */\n      'min-h': [{\n        'min-h': ['screen', 'lh', 'none', ...scaleSizing()]\n      }],\n      /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */\n      'max-h': [{\n        'max-h': ['screen', 'lh', ...scaleSizing()]\n      }],\n      // ------------------\n      // --- Typography ---\n      // ------------------\n      /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */\n      'font-size': [{\n        text: ['base', themeText, isArbitraryVariableLength, isArbitraryLength]\n      }],\n      /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */\n      'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n      /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */\n      'font-style': ['italic', 'not-italic'],\n      /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */\n      'font-weight': [{\n        font: [themeFontWeight, isArbitraryVariable, isArbitraryNumber]\n      }],\n      /**\n       * Font Stretch\n       * @see https://tailwindcss.com/docs/font-stretch\n       */\n      'font-stretch': [{\n        'font-stretch': ['ultra-condensed', 'extra-condensed', 'condensed', 'semi-condensed', 'normal', 'semi-expanded', 'expanded', 'extra-expanded', 'ultra-expanded', isPercent, isArbitraryValue]\n      }],\n      /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */\n      'font-family': [{\n        font: [isArbitraryVariableFamilyName, isArbitraryValue, themeFont]\n      }],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-normal': ['normal-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-ordinal': ['ordinal'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-slashed-zero': ['slashed-zero'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n      /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */\n      tracking: [{\n        tracking: [themeTracking, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */\n      'line-clamp': [{\n        'line-clamp': [isNumber, 'none', isArbitraryVariable, isArbitraryNumber]\n      }],\n      /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */\n      leading: [{\n        leading: [/** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        themeLeading, ...scaleUnambiguousSpacing()]\n      }],\n      /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */\n      'list-image': [{\n        'list-image': ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */\n      'list-style-position': [{\n        list: ['inside', 'outside']\n      }],\n      /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */\n      'list-style-type': [{\n        list: ['disc', 'decimal', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */\n      'text-alignment': [{\n        text: ['left', 'center', 'right', 'justify', 'start', 'end']\n      }],\n      /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://v3.tailwindcss.com/docs/placeholder-color\n       */\n      'placeholder-color': [{\n        placeholder: scaleColor()\n      }],\n      /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */\n      'text-color': [{\n        text: scaleColor()\n      }],\n      /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */\n      'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n      /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */\n      'text-decoration-style': [{\n        decoration: [...scaleLineStyle(), 'wavy']\n      }],\n      /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */\n      'text-decoration-thickness': [{\n        decoration: [isNumber, 'from-font', 'auto', isArbitraryVariable, isArbitraryLength]\n      }],\n      /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */\n      'text-decoration-color': [{\n        decoration: scaleColor()\n      }],\n      /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */\n      'underline-offset': [{\n        'underline-offset': [isNumber, 'auto', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */\n      'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n      /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */\n      'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n      /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */\n      'text-wrap': [{\n        text: ['wrap', 'nowrap', 'balance', 'pretty']\n      }],\n      /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */\n      indent: [{\n        indent: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */\n      'vertical-align': [{\n        align: ['baseline', 'top', 'middle', 'bottom', 'text-top', 'text-bottom', 'sub', 'super', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */\n      whitespace: [{\n        whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces']\n      }],\n      /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */\n      break: [{\n        break: ['normal', 'words', 'all', 'keep']\n      }],\n      /**\n       * Overflow Wrap\n       * @see https://tailwindcss.com/docs/overflow-wrap\n       */\n      wrap: [{\n        wrap: ['break-word', 'anywhere', 'normal']\n      }],\n      /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */\n      hyphens: [{\n        hyphens: ['none', 'manual', 'auto']\n      }],\n      /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */\n      content: [{\n        content: ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      // -------------------\n      // --- Backgrounds ---\n      // -------------------\n      /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */\n      'bg-attachment': [{\n        bg: ['fixed', 'local', 'scroll']\n      }],\n      /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */\n      'bg-clip': [{\n        'bg-clip': ['border', 'padding', 'content', 'text']\n      }],\n      /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */\n      'bg-origin': [{\n        'bg-origin': ['border', 'padding', 'content']\n      }],\n      /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */\n      'bg-position': [{\n        bg: scaleBgPosition()\n      }],\n      /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */\n      'bg-repeat': [{\n        bg: scaleBgRepeat()\n      }],\n      /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */\n      'bg-size': [{\n        bg: scaleBgSize()\n      }],\n      /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */\n      'bg-image': [{\n        bg: ['none', {\n          linear: [{\n            to: ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl']\n          }, isInteger, isArbitraryVariable, isArbitraryValue],\n          radial: ['', isArbitraryVariable, isArbitraryValue],\n          conic: [isInteger, isArbitraryVariable, isArbitraryValue]\n        }, isArbitraryVariableImage, isArbitraryImage]\n      }],\n      /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */\n      'bg-color': [{\n        bg: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from-pos': [{\n        from: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via-pos': [{\n        via: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to-pos': [{\n        to: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from': [{\n        from: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via': [{\n        via: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to': [{\n        to: scaleColor()\n      }],\n      // ---------------\n      // --- Borders ---\n      // ---------------\n      /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      rounded: [{\n        rounded: scaleRadius()\n      }],\n      /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-s': [{\n        'rounded-s': scaleRadius()\n      }],\n      /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-e': [{\n        'rounded-e': scaleRadius()\n      }],\n      /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-t': [{\n        'rounded-t': scaleRadius()\n      }],\n      /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-r': [{\n        'rounded-r': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-b': [{\n        'rounded-b': scaleRadius()\n      }],\n      /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-l': [{\n        'rounded-l': scaleRadius()\n      }],\n      /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ss': [{\n        'rounded-ss': scaleRadius()\n      }],\n      /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-se': [{\n        'rounded-se': scaleRadius()\n      }],\n      /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ee': [{\n        'rounded-ee': scaleRadius()\n      }],\n      /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-es': [{\n        'rounded-es': scaleRadius()\n      }],\n      /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tl': [{\n        'rounded-tl': scaleRadius()\n      }],\n      /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tr': [{\n        'rounded-tr': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-br': [{\n        'rounded-br': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-bl': [{\n        'rounded-bl': scaleRadius()\n      }],\n      /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w': [{\n        border: scaleBorderWidth()\n      }],\n      /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-x': [{\n        'border-x': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-y': [{\n        'border-y': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-s': [{\n        'border-s': scaleBorderWidth()\n      }],\n      /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-e': [{\n        'border-e': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-t': [{\n        'border-t': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-r': [{\n        'border-r': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-b': [{\n        'border-b': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-l': [{\n        'border-l': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-x': [{\n        'divide-x': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-x-reverse': ['divide-x-reverse'],\n      /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-y': [{\n        'divide-y': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-y-reverse': ['divide-y-reverse'],\n      /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */\n      'border-style': [{\n        border: [...scaleLineStyle(), 'hidden', 'none']\n      }],\n      /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/border-style#setting-the-divider-style\n       */\n      'divide-style': [{\n        divide: [...scaleLineStyle(), 'hidden', 'none']\n      }],\n      /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color': [{\n        border: scaleColor()\n      }],\n      /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-x': [{\n        'border-x': scaleColor()\n      }],\n      /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-y': [{\n        'border-y': scaleColor()\n      }],\n      /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-s': [{\n        'border-s': scaleColor()\n      }],\n      /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-e': [{\n        'border-e': scaleColor()\n      }],\n      /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-t': [{\n        'border-t': scaleColor()\n      }],\n      /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-r': [{\n        'border-r': scaleColor()\n      }],\n      /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-b': [{\n        'border-b': scaleColor()\n      }],\n      /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-l': [{\n        'border-l': scaleColor()\n      }],\n      /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */\n      'divide-color': [{\n        divide: scaleColor()\n      }],\n      /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */\n      'outline-style': [{\n        outline: [...scaleLineStyle(), 'none', 'hidden']\n      }],\n      /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */\n      'outline-offset': [{\n        'outline-offset': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */\n      'outline-w': [{\n        outline: ['', isNumber, isArbitraryVariableLength, isArbitraryLength]\n      }],\n      /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */\n      'outline-color': [{\n        outline: scaleColor()\n      }],\n      // ---------------\n      // --- Effects ---\n      // ---------------\n      /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */\n      shadow: [{\n        shadow: [\n        // Deprecated since Tailwind CSS v4.0.0\n        '', 'none', themeShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-shadow-color\n       */\n      'shadow-color': [{\n        shadow: scaleColor()\n      }],\n      /**\n       * Inset Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-shadow\n       */\n      'inset-shadow': [{\n        'inset-shadow': ['none', themeInsetShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Inset Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-shadow-color\n       */\n      'inset-shadow-color': [{\n        'inset-shadow': scaleColor()\n      }],\n      /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-a-ring\n       */\n      'ring-w': [{\n        ring: scaleBorderWidth()\n      }],\n      /**\n       * Ring Width Inset\n       * @see https://v3.tailwindcss.com/docs/ring-width#inset-rings\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-w-inset': ['ring-inset'],\n      /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-ring-color\n       */\n      'ring-color': [{\n        ring: scaleColor()\n      }],\n      /**\n       * Ring Offset Width\n       * @see https://v3.tailwindcss.com/docs/ring-offset-width\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-offset-w': [{\n        'ring-offset': [isNumber, isArbitraryLength]\n      }],\n      /**\n       * Ring Offset Color\n       * @see https://v3.tailwindcss.com/docs/ring-offset-color\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-offset-color': [{\n        'ring-offset': scaleColor()\n      }],\n      /**\n       * Inset Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-ring\n       */\n      'inset-ring-w': [{\n        'inset-ring': scaleBorderWidth()\n      }],\n      /**\n       * Inset Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-ring-color\n       */\n      'inset-ring-color': [{\n        'inset-ring': scaleColor()\n      }],\n      /**\n       * Text Shadow\n       * @see https://tailwindcss.com/docs/text-shadow\n       */\n      'text-shadow': [{\n        'text-shadow': ['none', themeTextShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Text Shadow Color\n       * @see https://tailwindcss.com/docs/text-shadow#setting-the-shadow-color\n       */\n      'text-shadow-color': [{\n        'text-shadow': scaleColor()\n      }],\n      /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */\n      opacity: [{\n        opacity: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */\n      'mix-blend': [{\n        'mix-blend': [...scaleBlendMode(), 'plus-darker', 'plus-lighter']\n      }],\n      /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */\n      'bg-blend': [{\n        'bg-blend': scaleBlendMode()\n      }],\n      /**\n       * Mask Clip\n       * @see https://tailwindcss.com/docs/mask-clip\n       */\n      'mask-clip': [{\n        'mask-clip': ['border', 'padding', 'content', 'fill', 'stroke', 'view']\n      }, 'mask-no-clip'],\n      /**\n       * Mask Composite\n       * @see https://tailwindcss.com/docs/mask-composite\n       */\n      'mask-composite': [{\n        mask: ['add', 'subtract', 'intersect', 'exclude']\n      }],\n      /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */\n      'mask-image-linear-pos': [{\n        'mask-linear': [isNumber]\n      }],\n      'mask-image-linear-from-pos': [{\n        'mask-linear-from': scaleMaskImagePosition()\n      }],\n      'mask-image-linear-to-pos': [{\n        'mask-linear-to': scaleMaskImagePosition()\n      }],\n      'mask-image-linear-from-color': [{\n        'mask-linear-from': scaleColor()\n      }],\n      'mask-image-linear-to-color': [{\n        'mask-linear-to': scaleColor()\n      }],\n      'mask-image-t-from-pos': [{\n        'mask-t-from': scaleMaskImagePosition()\n      }],\n      'mask-image-t-to-pos': [{\n        'mask-t-to': scaleMaskImagePosition()\n      }],\n      'mask-image-t-from-color': [{\n        'mask-t-from': scaleColor()\n      }],\n      'mask-image-t-to-color': [{\n        'mask-t-to': scaleColor()\n      }],\n      'mask-image-r-from-pos': [{\n        'mask-r-from': scaleMaskImagePosition()\n      }],\n      'mask-image-r-to-pos': [{\n        'mask-r-to': scaleMaskImagePosition()\n      }],\n      'mask-image-r-from-color': [{\n        'mask-r-from': scaleColor()\n      }],\n      'mask-image-r-to-color': [{\n        'mask-r-to': scaleColor()\n      }],\n      'mask-image-b-from-pos': [{\n        'mask-b-from': scaleMaskImagePosition()\n      }],\n      'mask-image-b-to-pos': [{\n        'mask-b-to': scaleMaskImagePosition()\n      }],\n      'mask-image-b-from-color': [{\n        'mask-b-from': scaleColor()\n      }],\n      'mask-image-b-to-color': [{\n        'mask-b-to': scaleColor()\n      }],\n      'mask-image-l-from-pos': [{\n        'mask-l-from': scaleMaskImagePosition()\n      }],\n      'mask-image-l-to-pos': [{\n        'mask-l-to': scaleMaskImagePosition()\n      }],\n      'mask-image-l-from-color': [{\n        'mask-l-from': scaleColor()\n      }],\n      'mask-image-l-to-color': [{\n        'mask-l-to': scaleColor()\n      }],\n      'mask-image-x-from-pos': [{\n        'mask-x-from': scaleMaskImagePosition()\n      }],\n      'mask-image-x-to-pos': [{\n        'mask-x-to': scaleMaskImagePosition()\n      }],\n      'mask-image-x-from-color': [{\n        'mask-x-from': scaleColor()\n      }],\n      'mask-image-x-to-color': [{\n        'mask-x-to': scaleColor()\n      }],\n      'mask-image-y-from-pos': [{\n        'mask-y-from': scaleMaskImagePosition()\n      }],\n      'mask-image-y-to-pos': [{\n        'mask-y-to': scaleMaskImagePosition()\n      }],\n      'mask-image-y-from-color': [{\n        'mask-y-from': scaleColor()\n      }],\n      'mask-image-y-to-color': [{\n        'mask-y-to': scaleColor()\n      }],\n      'mask-image-radial': [{\n        'mask-radial': [isArbitraryVariable, isArbitraryValue]\n      }],\n      'mask-image-radial-from-pos': [{\n        'mask-radial-from': scaleMaskImagePosition()\n      }],\n      'mask-image-radial-to-pos': [{\n        'mask-radial-to': scaleMaskImagePosition()\n      }],\n      'mask-image-radial-from-color': [{\n        'mask-radial-from': scaleColor()\n      }],\n      'mask-image-radial-to-color': [{\n        'mask-radial-to': scaleColor()\n      }],\n      'mask-image-radial-shape': [{\n        'mask-radial': ['circle', 'ellipse']\n      }],\n      'mask-image-radial-size': [{\n        'mask-radial': [{\n          closest: ['side', 'corner'],\n          farthest: ['side', 'corner']\n        }]\n      }],\n      'mask-image-radial-pos': [{\n        'mask-radial-at': scalePosition()\n      }],\n      'mask-image-conic-pos': [{\n        'mask-conic': [isNumber]\n      }],\n      'mask-image-conic-from-pos': [{\n        'mask-conic-from': scaleMaskImagePosition()\n      }],\n      'mask-image-conic-to-pos': [{\n        'mask-conic-to': scaleMaskImagePosition()\n      }],\n      'mask-image-conic-from-color': [{\n        'mask-conic-from': scaleColor()\n      }],\n      'mask-image-conic-to-color': [{\n        'mask-conic-to': scaleColor()\n      }],\n      /**\n       * Mask Mode\n       * @see https://tailwindcss.com/docs/mask-mode\n       */\n      'mask-mode': [{\n        mask: ['alpha', 'luminance', 'match']\n      }],\n      /**\n       * Mask Origin\n       * @see https://tailwindcss.com/docs/mask-origin\n       */\n      'mask-origin': [{\n        'mask-origin': ['border', 'padding', 'content', 'fill', 'stroke', 'view']\n      }],\n      /**\n       * Mask Position\n       * @see https://tailwindcss.com/docs/mask-position\n       */\n      'mask-position': [{\n        mask: scaleBgPosition()\n      }],\n      /**\n       * Mask Repeat\n       * @see https://tailwindcss.com/docs/mask-repeat\n       */\n      'mask-repeat': [{\n        mask: scaleBgRepeat()\n      }],\n      /**\n       * Mask Size\n       * @see https://tailwindcss.com/docs/mask-size\n       */\n      'mask-size': [{\n        mask: scaleBgSize()\n      }],\n      /**\n       * Mask Type\n       * @see https://tailwindcss.com/docs/mask-type\n       */\n      'mask-type': [{\n        'mask-type': ['alpha', 'luminance']\n      }],\n      /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */\n      'mask-image': [{\n        mask: ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      // ---------------\n      // --- Filters ---\n      // ---------------\n      /**\n       * Filter\n       * @see https://tailwindcss.com/docs/filter\n       */\n      filter: [{\n        filter: [\n        // Deprecated since Tailwind CSS v3.0.0\n        '', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */\n      blur: [{\n        blur: scaleBlur()\n      }],\n      /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */\n      brightness: [{\n        brightness: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */\n      contrast: [{\n        contrast: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */\n      'drop-shadow': [{\n        'drop-shadow': [\n        // Deprecated since Tailwind CSS v4.0.0\n        '', 'none', themeDropShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Drop Shadow Color\n       * @see https://tailwindcss.com/docs/filter-drop-shadow#setting-the-shadow-color\n       */\n      'drop-shadow-color': [{\n        'drop-shadow': scaleColor()\n      }],\n      /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */\n      grayscale: [{\n        grayscale: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */\n      'hue-rotate': [{\n        'hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */\n      invert: [{\n        invert: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */\n      saturate: [{\n        saturate: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */\n      sepia: [{\n        sepia: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Filter\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */\n      'backdrop-filter': [{\n        'backdrop-filter': [\n        // Deprecated since Tailwind CSS v3.0.0\n        '', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */\n      'backdrop-blur': [{\n        'backdrop-blur': scaleBlur()\n      }],\n      /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */\n      'backdrop-brightness': [{\n        'backdrop-brightness': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */\n      'backdrop-contrast': [{\n        'backdrop-contrast': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */\n      'backdrop-grayscale': [{\n        'backdrop-grayscale': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */\n      'backdrop-hue-rotate': [{\n        'backdrop-hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */\n      'backdrop-invert': [{\n        'backdrop-invert': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */\n      'backdrop-opacity': [{\n        'backdrop-opacity': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */\n      'backdrop-saturate': [{\n        'backdrop-saturate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */\n      'backdrop-sepia': [{\n        'backdrop-sepia': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      // --------------\n      // --- Tables ---\n      // --------------\n      /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */\n      'border-collapse': [{\n        border: ['collapse', 'separate']\n      }],\n      /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing': [{\n        'border-spacing': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-x': [{\n        'border-spacing-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-y': [{\n        'border-spacing-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */\n      'table-layout': [{\n        table: ['auto', 'fixed']\n      }],\n      /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */\n      caption: [{\n        caption: ['top', 'bottom']\n      }],\n      // ---------------------------------\n      // --- Transitions and Animation ---\n      // ---------------------------------\n      /**\n       * Transition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */\n      transition: [{\n        transition: ['', 'all', 'colors', 'opacity', 'shadow', 'transform', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Behavior\n       * @see https://tailwindcss.com/docs/transition-behavior\n       */\n      'transition-behavior': [{\n        transition: ['normal', 'discrete']\n      }],\n      /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */\n      duration: [{\n        duration: [isNumber, 'initial', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */\n      ease: [{\n        ease: ['linear', 'initial', themeEase, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */\n      delay: [{\n        delay: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */\n      animate: [{\n        animate: ['none', themeAnimate, isArbitraryVariable, isArbitraryValue]\n      }],\n      // ------------------\n      // --- Transforms ---\n      // ------------------\n      /**\n       * Backface Visibility\n       * @see https://tailwindcss.com/docs/backface-visibility\n       */\n      backface: [{\n        backface: ['hidden', 'visible']\n      }],\n      /**\n       * Perspective\n       * @see https://tailwindcss.com/docs/perspective\n       */\n      perspective: [{\n        perspective: [themePerspective, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Perspective Origin\n       * @see https://tailwindcss.com/docs/perspective-origin\n       */\n      'perspective-origin': [{\n        'perspective-origin': scalePositionWithArbitrary()\n      }],\n      /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      rotate: [{\n        rotate: scaleRotate()\n      }],\n      /**\n       * Rotate X\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-x': [{\n        'rotate-x': scaleRotate()\n      }],\n      /**\n       * Rotate Y\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-y': [{\n        'rotate-y': scaleRotate()\n      }],\n      /**\n       * Rotate Z\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-z': [{\n        'rotate-z': scaleRotate()\n      }],\n      /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */\n      scale: [{\n        scale: scaleScale()\n      }],\n      /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-x': [{\n        'scale-x': scaleScale()\n      }],\n      /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-y': [{\n        'scale-y': scaleScale()\n      }],\n      /**\n       * Scale Z\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-z': [{\n        'scale-z': scaleScale()\n      }],\n      /**\n       * Scale 3D\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-3d': ['scale-3d'],\n      /**\n       * Skew\n       * @see https://tailwindcss.com/docs/skew\n       */\n      skew: [{\n        skew: scaleSkew()\n      }],\n      /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-x': [{\n        'skew-x': scaleSkew()\n      }],\n      /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-y': [{\n        'skew-y': scaleSkew()\n      }],\n      /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */\n      transform: [{\n        transform: [isArbitraryVariable, isArbitraryValue, '', 'none', 'gpu', 'cpu']\n      }],\n      /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */\n      'transform-origin': [{\n        origin: scalePositionWithArbitrary()\n      }],\n      /**\n       * Transform Style\n       * @see https://tailwindcss.com/docs/transform-style\n       */\n      'transform-style': [{\n        transform: ['3d', 'flat']\n      }],\n      /**\n       * Translate\n       * @see https://tailwindcss.com/docs/translate\n       */\n      translate: [{\n        translate: scaleTranslate()\n      }],\n      /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-x': [{\n        'translate-x': scaleTranslate()\n      }],\n      /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-y': [{\n        'translate-y': scaleTranslate()\n      }],\n      /**\n       * Translate Z\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-z': [{\n        'translate-z': scaleTranslate()\n      }],\n      /**\n       * Translate None\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-none': ['translate-none'],\n      // ---------------------\n      // --- Interactivity ---\n      // ---------------------\n      /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */\n      accent: [{\n        accent: scaleColor()\n      }],\n      /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */\n      appearance: [{\n        appearance: ['none', 'auto']\n      }],\n      /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */\n      'caret-color': [{\n        caret: scaleColor()\n      }],\n      /**\n       * Color Scheme\n       * @see https://tailwindcss.com/docs/color-scheme\n       */\n      'color-scheme': [{\n        scheme: ['normal', 'dark', 'light', 'light-dark', 'only-dark', 'only-light']\n      }],\n      /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */\n      cursor: [{\n        cursor: ['auto', 'default', 'pointer', 'wait', 'text', 'move', 'help', 'not-allowed', 'none', 'context-menu', 'progress', 'cell', 'crosshair', 'vertical-text', 'alias', 'copy', 'no-drop', 'grab', 'grabbing', 'all-scroll', 'col-resize', 'row-resize', 'n-resize', 'e-resize', 's-resize', 'w-resize', 'ne-resize', 'nw-resize', 'se-resize', 'sw-resize', 'ew-resize', 'ns-resize', 'nesw-resize', 'nwse-resize', 'zoom-in', 'zoom-out', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Field Sizing\n       * @see https://tailwindcss.com/docs/field-sizing\n       */\n      'field-sizing': [{\n        'field-sizing': ['fixed', 'content']\n      }],\n      /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */\n      'pointer-events': [{\n        'pointer-events': ['auto', 'none']\n      }],\n      /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */\n      resize: [{\n        resize: ['none', '', 'y', 'x']\n      }],\n      /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */\n      'scroll-behavior': [{\n        scroll: ['auto', 'smooth']\n      }],\n      /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-m': [{\n        'scroll-m': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mx': [{\n        'scroll-mx': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-my': [{\n        'scroll-my': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ms': [{\n        'scroll-ms': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-me': [{\n        'scroll-me': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mt': [{\n        'scroll-mt': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mr': [{\n        'scroll-mr': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mb': [{\n        'scroll-mb': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ml': [{\n        'scroll-ml': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-p': [{\n        'scroll-p': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-px': [{\n        'scroll-px': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-py': [{\n        'scroll-py': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-ps': [{\n        'scroll-ps': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pe': [{\n        'scroll-pe': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pt': [{\n        'scroll-pt': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pr': [{\n        'scroll-pr': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pb': [{\n        'scroll-pb': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pl': [{\n        'scroll-pl': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */\n      'snap-align': [{\n        snap: ['start', 'end', 'center', 'align-none']\n      }],\n      /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */\n      'snap-stop': [{\n        snap: ['normal', 'always']\n      }],\n      /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-type': [{\n        snap: ['none', 'x', 'y', 'both']\n      }],\n      /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-strictness': [{\n        snap: ['mandatory', 'proximity']\n      }],\n      /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      touch: [{\n        touch: ['auto', 'none', 'manipulation']\n      }],\n      /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-x': [{\n        'touch-pan': ['x', 'left', 'right']\n      }],\n      /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-y': [{\n        'touch-pan': ['y', 'up', 'down']\n      }],\n      /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-pz': ['touch-pinch-zoom'],\n      /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */\n      select: [{\n        select: ['none', 'text', 'all', 'auto']\n      }],\n      /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */\n      'will-change': [{\n        'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryVariable, isArbitraryValue]\n      }],\n      // -----------\n      // --- SVG ---\n      // -----------\n      /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */\n      fill: [{\n        fill: ['none', ...scaleColor()]\n      }],\n      /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */\n      'stroke-w': [{\n        stroke: [isNumber, isArbitraryVariableLength, isArbitraryLength, isArbitraryNumber]\n      }],\n      /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */\n      stroke: [{\n        stroke: ['none', ...scaleColor()]\n      }],\n      // ---------------------\n      // --- Accessibility ---\n      // ---------------------\n      /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */\n      'forced-color-adjust': [{\n        'forced-color-adjust': ['auto', 'none']\n      }]\n    },\n    conflictingClassGroups: {\n      overflow: ['overflow-x', 'overflow-y'],\n      overscroll: ['overscroll-x', 'overscroll-y'],\n      inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n      'inset-x': ['right', 'left'],\n      'inset-y': ['top', 'bottom'],\n      flex: ['basis', 'grow', 'shrink'],\n      gap: ['gap-x', 'gap-y'],\n      p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n      px: ['pr', 'pl'],\n      py: ['pt', 'pb'],\n      m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n      mx: ['mr', 'ml'],\n      my: ['mt', 'mb'],\n      size: ['w', 'h'],\n      'font-size': ['leading'],\n      'fvn-normal': ['fvn-ordinal', 'fvn-slashed-zero', 'fvn-figure', 'fvn-spacing', 'fvn-fraction'],\n      'fvn-ordinal': ['fvn-normal'],\n      'fvn-slashed-zero': ['fvn-normal'],\n      'fvn-figure': ['fvn-normal'],\n      'fvn-spacing': ['fvn-normal'],\n      'fvn-fraction': ['fvn-normal'],\n      'line-clamp': ['display', 'overflow'],\n      rounded: ['rounded-s', 'rounded-e', 'rounded-t', 'rounded-r', 'rounded-b', 'rounded-l', 'rounded-ss', 'rounded-se', 'rounded-ee', 'rounded-es', 'rounded-tl', 'rounded-tr', 'rounded-br', 'rounded-bl'],\n      'rounded-s': ['rounded-ss', 'rounded-es'],\n      'rounded-e': ['rounded-se', 'rounded-ee'],\n      'rounded-t': ['rounded-tl', 'rounded-tr'],\n      'rounded-r': ['rounded-tr', 'rounded-br'],\n      'rounded-b': ['rounded-br', 'rounded-bl'],\n      'rounded-l': ['rounded-tl', 'rounded-bl'],\n      'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n      'border-w': ['border-w-x', 'border-w-y', 'border-w-s', 'border-w-e', 'border-w-t', 'border-w-r', 'border-w-b', 'border-w-l'],\n      'border-w-x': ['border-w-r', 'border-w-l'],\n      'border-w-y': ['border-w-t', 'border-w-b'],\n      'border-color': ['border-color-x', 'border-color-y', 'border-color-s', 'border-color-e', 'border-color-t', 'border-color-r', 'border-color-b', 'border-color-l'],\n      'border-color-x': ['border-color-r', 'border-color-l'],\n      'border-color-y': ['border-color-t', 'border-color-b'],\n      translate: ['translate-x', 'translate-y', 'translate-none'],\n      'translate-none': ['translate', 'translate-x', 'translate-y', 'translate-z'],\n      'scroll-m': ['scroll-mx', 'scroll-my', 'scroll-ms', 'scroll-me', 'scroll-mt', 'scroll-mr', 'scroll-mb', 'scroll-ml'],\n      'scroll-mx': ['scroll-mr', 'scroll-ml'],\n      'scroll-my': ['scroll-mt', 'scroll-mb'],\n      'scroll-p': ['scroll-px', 'scroll-py', 'scroll-ps', 'scroll-pe', 'scroll-pt', 'scroll-pr', 'scroll-pb', 'scroll-pl'],\n      'scroll-px': ['scroll-pr', 'scroll-pl'],\n      'scroll-py': ['scroll-pt', 'scroll-pb'],\n      touch: ['touch-x', 'touch-y', 'touch-pz'],\n      'touch-x': ['touch'],\n      'touch-y': ['touch'],\n      'touch-pz': ['touch']\n    },\n    conflictingClassGroupModifiers: {\n      'font-size': ['leading']\n    },\n    orderSensitiveModifiers: ['*', '**', 'after', 'backdrop', 'before', 'details-content', 'file', 'first-letter', 'first-line', 'marker', 'placeholder', 'selection']\n  };\n};\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nconst mergeConfigs = (baseConfig, {\n  cacheSize,\n  prefix,\n  experimentalParseClassName,\n  extend = {},\n  override = {}\n}) => {\n  overrideProperty(baseConfig, 'cacheSize', cacheSize);\n  overrideProperty(baseConfig, 'prefix', prefix);\n  overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n  overrideConfigProperties(baseConfig.theme, override.theme);\n  overrideConfigProperties(baseConfig.classGroups, override.classGroups);\n  overrideConfigProperties(baseConfig.conflictingClassGroups, override.conflictingClassGroups);\n  overrideConfigProperties(baseConfig.conflictingClassGroupModifiers, override.conflictingClassGroupModifiers);\n  overrideProperty(baseConfig, 'orderSensitiveModifiers', override.orderSensitiveModifiers);\n  mergeConfigProperties(baseConfig.theme, extend.theme);\n  mergeConfigProperties(baseConfig.classGroups, extend.classGroups);\n  mergeConfigProperties(baseConfig.conflictingClassGroups, extend.conflictingClassGroups);\n  mergeConfigProperties(baseConfig.conflictingClassGroupModifiers, extend.conflictingClassGroupModifiers);\n  mergeArrayProperties(baseConfig, extend, 'orderSensitiveModifiers');\n  return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue) => {\n  if (overrideValue !== undefined) {\n    baseObject[overrideKey] = overrideValue;\n  }\n};\nconst overrideConfigProperties = (baseObject, overrideObject) => {\n  if (overrideObject) {\n    for (const key in overrideObject) {\n      overrideProperty(baseObject, key, overrideObject[key]);\n    }\n  }\n};\nconst mergeConfigProperties = (baseObject, mergeObject) => {\n  if (mergeObject) {\n    for (const key in mergeObject) {\n      mergeArrayProperties(baseObject, mergeObject, key);\n    }\n  }\n};\nconst mergeArrayProperties = (baseObject, mergeObject, key) => {\n  const mergeValue = mergeObject[key];\n  if (mergeValue !== undefined) {\n    baseObject[key] = baseObject[key] ? baseObject[key].concat(mergeValue) : mergeValue;\n  }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig) => typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(() => mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/createTailwindMerge(getDefaultConfig);\n\n//# sourceMappingURL=bundle-mjs.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const { user, logout, hasPermission } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    // Mock data for demonstration\n    const todaysCases = [\n        {\n            id: 1,\n            caseNumber: 'CR 45/2024 MC',\n            title: 'Republic vs John Doe',\n            type: 'Criminal',\n            status: 'HEARING',\n            time: '09:00 AM',\n            priority: 'HIGH'\n        },\n        {\n            id: 2,\n            caseNumber: 'CV 23/2024 MC',\n            title: 'Jane Smith vs ABC Company',\n            type: 'Civil',\n            status: 'MENTION',\n            time: '10:30 AM',\n            priority: 'MEDIUM'\n        },\n        {\n            id: 3,\n            caseNumber: 'CR 67/2024 MC',\n            title: 'Republic vs Mary Johnson',\n            type: 'Criminal',\n            status: 'JUDGMENT',\n            time: '02:00 PM',\n            priority: 'HIGH'\n        }\n    ];\n    // Role-based quick actions\n    const allQuickActions = [\n        {\n            title: 'Start New Case',\n            description: 'Begin a new case proceeding',\n            permission: 'create_case',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-blue-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>alert('Start New Case - Feature coming soon!')\n        },\n        {\n            title: 'View Court List',\n            description: 'See today\\'s scheduled cases',\n            permission: 'manage_court_list',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-green-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>window.location.href = '/court-list'\n        },\n        {\n            title: 'Start Recording',\n            description: 'Begin court session transcription',\n            permission: 'start_recording',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-red-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>window.location.href = '/transcription'\n        },\n        {\n            title: 'Draft Judgment',\n            description: 'Create or edit court judgments',\n            permission: 'draft_judgment',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-purple-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>window.location.href = '/documents'\n        },\n        {\n            title: 'View My Cases',\n            description: 'See cases assigned to you',\n            permission: 'view_own_cases',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-indigo-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>alert('My Cases - Feature coming soon!')\n        }\n    ];\n    // Filter actions based on user permissions\n    const quickActions = allQuickActions.filter((action)=>hasPermission(action.permission));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l-3-9m3 9l3-9\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"KesiTrack\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Legal Case Management System\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatLegalDate)(currentTime)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCourtTime)(currentTime)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"secondary\",\n                                                size: \"medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    (user === null || user === void 0 ? void 0 : user.displayName) || (user === null || user === void 0 ? void 0 : user.username)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"danger\",\n                                                size: \"medium\",\n                                                onClick: logout,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Sign Out\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Welcome to KesiTrack\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600\",\n                                children: \"Your AI-powered legal case management system for efficient court proceedings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Quick Actions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: quickActions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.QuickActionCard, {\n                                        title: action.title,\n                                        description: action.description,\n                                        icon: action.icon,\n                                        onClick: action.onClick\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: [\n                                    \"Today's Court List - \",\n                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatLegalDate)(new Date())\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: todaysCases.map((case_)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.StatusCard, {\n                                        title: \"\".concat(case_.caseNumber, \" - \").concat(case_.title),\n                                        status: case_.status,\n                                        description: \"\".concat(case_.type, \" case scheduled for \").concat(case_.time),\n                                        statusColor: case_.status === 'HEARING' ? 'status-active' : case_.status === 'JUDGMENT' ? 'status-pending' : 'status-pending',\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-blue-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"primary\",\n                                            size: \"small\",\n                                            children: \"Open Case\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    }, case_.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.InfoCard, {\n                                type: \"info\",\n                                title: \"System Status\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"All systems are operational. Transcription service is active and ready for court proceedings.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.InfoCard, {\n                                type: \"success\",\n                                title: \"Recent Updates\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Enhanced AI transcription accuracy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"New judgment templates added\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Improved case search functionality\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"FuTLB10cBDOXIDS2kg/K/Mm2N9E=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = Dashboard;\n// Export the component with authentication protection\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_c1 = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.withAuth)(Dashboard));\nvar _c, _c1;\n$RefreshReg$(_c, \"Dashboard\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   ButtonGroup: () => (/* binding */ ButtonGroup),\n/* harmony export */   IconButton: () => (/* binding */ IconButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n/**\n * Large, accessible button component designed for non-technical users\n * Features:\n * - Large touch targets (minimum 44px)\n * - Clear visual feedback\n * - Loading states\n * - High contrast colors\n * - Keyboard navigation support\n */ function Button(param) {\n    let { variant = 'primary', size = 'medium', loading = false, icon, children, className, disabled, ...props } = param;\n    const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed';\n    const variantClasses = {\n        primary: 'btn-primary focus:ring-blue-300',\n        secondary: 'btn-secondary focus:ring-gray-300',\n        success: 'btn-success focus:ring-green-300',\n        warning: 'btn-warning focus:ring-yellow-300',\n        danger: 'btn-danger focus:ring-red-300'\n    };\n    const sizeClasses = {\n        small: 'px-4 py-2 text-base min-h-[40px]',\n        medium: 'px-6 py-3 text-lg min-h-[48px]',\n        large: 'px-8 py-4 text-xl min-h-[56px]'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variantClasses[variant], sizeClasses[size], className),\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-3 h-5 w-5\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            icon && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 80,\n                columnNumber: 28\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_c = Button;\n/**\n * Icon-only button for actions like edit, delete, etc.\n */ function IconButton(param) {\n    let { variant = 'secondary', size = 'medium', loading = false, icon, className, disabled, 'aria-label': ariaLabel, ...props } = param;\n    const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed';\n    const variantClasses = {\n        primary: 'btn-primary focus:ring-blue-300',\n        secondary: 'btn-secondary focus:ring-gray-300',\n        success: 'btn-success focus:ring-green-300',\n        warning: 'btn-warning focus:ring-yellow-300',\n        danger: 'btn-danger focus:ring-red-300'\n    };\n    const sizeClasses = {\n        small: 'p-2 min-h-[40px] min-w-[40px]',\n        medium: 'p-3 min-h-[48px] min-w-[48px]',\n        large: 'p-4 min-h-[56px] min-w-[56px]'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variantClasses[variant], sizeClasses[size], className),\n        disabled: disabled || loading,\n        \"aria-label\": ariaLabel,\n        ...props,\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"animate-spin h-5 w-5\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    className: \"opacity-25\",\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"10\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    className: \"opacity-75\",\n                    fill: \"currentColor\",\n                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n            lineNumber: 128,\n            columnNumber: 9\n        }, this) : icon\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n_c1 = IconButton;\n/**\n * Button group for related actions\n */ function ButtonGroup(param) {\n    let { children, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex flex-wrap gap-3', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ButtonGroup;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Button\");\n$RefreshReg$(_c1, \"IconButton\");\n$RefreshReg$(_c2, \"ButtonGroup\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/Button.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   InfoCard: () => (/* binding */ InfoCard),\n/* harmony export */   QuickActionCard: () => (/* binding */ QuickActionCard),\n/* harmony export */   StatusCard: () => (/* binding */ StatusCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n/**\n * Card component for organizing content in a clean, accessible way\n * Designed for easy scanning by judicial officers\n */ function Card(param) {\n    let { children, className, padding = 'medium' } = param;\n    const paddingClasses = {\n        none: 'p-0',\n        small: 'p-4',\n        medium: 'p-6',\n        large: 'p-8'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('card', paddingClasses[padding], className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_c = Card;\n/**\n * Card header with optional actions\n */ function CardHeader(param) {\n    let { children, className, actions } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('card-header', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                actions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 ml-4\",\n                    children: actions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CardHeader;\n/**\n * Card content area\n */ function CardContent(param) {\n    let { children, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('space-y-4', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_c2 = CardContent;\n/**\n * Card footer for actions or additional info\n */ function CardFooter(param) {\n    let { children, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('pt-4 mt-6 border-t border-gray-200', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_c3 = CardFooter;\nfunction StatusCard(param) {\n    let { title, status, description, statusColor = 'status-pending', icon, actions, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('hover:shadow-lg transition-shadow duration-200', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3\",\n                        children: [\n                            icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0 mt-1\",\n                                children: icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('status-badge', statusColor),\n                                            children: status\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-base\",\n                                        children: description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this),\n                    actions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 ml-4\",\n                        children: actions\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n_c4 = StatusCard;\nfunction QuickActionCard(param) {\n    let { title, description, icon, onClick, className, disabled = false } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        disabled: disabled,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('card text-left w-full hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed', 'hover:scale-105 active:scale-95', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 p-3 bg-blue-50 rounded-lg\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-base\",\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6 text-gray-400\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 5l7 7-7 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_c5 = QuickActionCard;\nfunction InfoCard(param) {\n    let { type = 'info', title, children, className } = param;\n    const typeClasses = {\n        info: 'bg-blue-50 border-blue-200 text-blue-800',\n        warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n        success: 'bg-green-50 border-green-200 text-green-800',\n        error: 'bg-red-50 border-red-200 text-red-800'\n    };\n    const iconClasses = {\n        info: 'text-blue-500',\n        warning: 'text-yellow-500',\n        success: 'text-green-500',\n        error: 'text-red-500'\n    };\n    const icons = {\n        info: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 235,\n            columnNumber: 7\n        }, this),\n        warning: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, this),\n        success: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 246,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, this),\n        error: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 251,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 250,\n            columnNumber: 7\n        }, this)\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('rounded-lg border-2 p-4', typeClasses[type], className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex-shrink-0', iconClasses[type]),\n                    children: icons[type]\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-base\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 258,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 257,\n        columnNumber: 5\n    }, this);\n}\n_c6 = InfoCard;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"Card\");\n$RefreshReg$(_c1, \"CardHeader\");\n$RefreshReg$(_c2, \"CardContent\");\n$RefreshReg$(_c3, \"CardFooter\");\n$RefreshReg$(_c4, \"StatusCard\");\n$RefreshReg$(_c5, \"QuickActionCard\");\n$RefreshReg$(_c6, \"InfoCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/Card.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Role-based permissions\nconst PERMISSIONS = {\n    magistrate: [\n        'view_all_cases',\n        'create_case',\n        'edit_case',\n        'delete_case',\n        'start_recording',\n        'draft_judgment',\n        'finalize_judgment',\n        'manage_court_list',\n        'view_analytics',\n        'manage_templates',\n        'approve_documents'\n    ],\n    clerk: [\n        'view_assigned_cases',\n        'create_case',\n        'edit_case',\n        'manage_court_list',\n        'draft_documents',\n        'prepare_templates',\n        'schedule_hearings'\n    ],\n    advocate: [\n        'view_own_cases',\n        'view_case_status',\n        'view_documents',\n        'view_schedules'\n    ]\n};\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check for existing session on mount\n            const checkExistingSession = {\n                \"AuthProvider.useEffect.checkExistingSession\": ()=>{\n                    try {\n                        const storedUser = localStorage.getItem('kesitrack_user');\n                        if (storedUser) {\n                            const userData = JSON.parse(storedUser);\n                            // Check if session is still valid (24 hours)\n                            const loginTime = new Date(userData.loginTime);\n                            const now = new Date();\n                            const hoursDiff = (now.getTime() - loginTime.getTime()) / (1000 * 60 * 60);\n                            if (hoursDiff < 24) {\n                                setUser(userData);\n                            } else {\n                                // Session expired\n                                localStorage.removeItem('kesitrack_user');\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error checking session:', error);\n                        localStorage.removeItem('kesitrack_user');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkExistingSession\"];\n            checkExistingSession();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (username, password, role)=>{\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // For demo purposes, accept any credentials\n            // In real implementation, this would validate against backend\n            const userData = {\n                username,\n                role: role,\n                loginTime: new Date().toISOString(),\n                displayName: getDisplayName(username, role)\n            };\n            localStorage.setItem('kesitrack_user', JSON.stringify(userData));\n            setUser(userData);\n            return true;\n        } catch (error) {\n            console.error('Login error:', error);\n            return false;\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem('kesitrack_user');\n        setUser(null);\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        var _PERMISSIONS_user_role;\n        if (!user) return false;\n        return ((_PERMISSIONS_user_role = PERMISSIONS[user.role]) === null || _PERMISSIONS_user_role === void 0 ? void 0 : _PERMISSIONS_user_role.includes(permission)) || false;\n    };\n    const getDisplayName = (username, role)=>{\n        const roleNames = {\n            magistrate: 'Magistrate',\n            clerk: 'Court Clerk',\n            advocate: 'Advocate'\n        };\n        // In real implementation, this would come from user database\n        return \"\".concat(roleNames[role], \" \").concat(username);\n    };\n    const value = {\n        user,\n        login,\n        logout,\n        isLoading,\n        hasPermission\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Higher-order component for protecting routes\nfunction withAuth(Component, requiredPermissions) {\n    var _s = $RefreshSig$();\n    return _s(function AuthenticatedComponent(props) {\n        _s();\n        const { user, isLoading, hasPermission } = useAuth();\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-10 h-10 text-white animate-spin\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"Loading KesiTrack...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Please wait while we verify your session\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            window.location.href = '/login';\n            return null;\n        }\n        // Check permissions if required\n        if (requiredPermissions && requiredPermissions.length > 0) {\n            const hasRequiredPermissions = requiredPermissions.every((permission)=>hasPermission(permission));\n            if (!hasRequiredPermissions) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-10 h-10 text-red-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                children: \"Access Denied\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"You do not have permission to access this feature. Please contact your administrator if you believe this is an error.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.history.back(),\n                                className: \"btn-secondary\",\n                                children: \"Go Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 222,\n            columnNumber: 12\n        }, this);\n    }, \"+lqn/ZIAqPRflhHmBslixqkIPuo=\", false, function() {\n        return [\n            useAuth\n        ];\n    });\n}\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateBailAmount: () => (/* binding */ calculateBailAmount),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCourtTime: () => (/* binding */ formatCourtTime),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatKSh: () => (/* binding */ formatKSh),\n/* harmony export */   formatLegalDate: () => (/* binding */ formatLegalDate),\n/* harmony export */   generateCaseNumber: () => (/* binding */ generateCaseNumber),\n/* harmony export */   getCaseStatusColor: () => (/* binding */ getCaseStatusColor),\n/* harmony export */   getUserFriendlyError: () => (/* binding */ getUserFriendlyError),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   scrollToElement: () => (/* binding */ scrollToElement),\n/* harmony export */   simpleSearch: () => (/* binding */ simpleSearch),\n/* harmony export */   validateKenyanID: () => (/* binding */ validateKenyanID),\n/* harmony export */   validateKenyanPhone: () => (/* binding */ validateKenyanPhone)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Utility function to merge Tailwind CSS classes\n */ function cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format currency in Kenya Shillings\n */ function formatKSh(amount) {\n    return new Intl.NumberFormat('en-KE', {\n        style: 'currency',\n        currency: 'KES',\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).format(amount);\n}\n/**\n * Format date for Kenyan legal system\n */ function formatLegalDate(date) {\n    return new Intl.DateTimeFormat('en-KE', {\n        day: 'numeric',\n        month: 'long',\n        year: 'numeric'\n    }).format(date);\n}\n/**\n * Format time for court proceedings\n */ function formatCourtTime(date) {\n    return new Intl.DateTimeFormat('en-KE', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n    }).format(date);\n}\n/**\n * Generate case number format\n */ function generateCaseNumber(type, year, sequence) {\n    let court = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 'MC';\n    const typeCode = {\n        CRIMINAL: 'CR',\n        CIVIL: 'CV',\n        FAMILY: 'FM',\n        COMMERCIAL: 'CM'\n    }[type];\n    return \"\".concat(typeCode, \" \").concat(sequence, \"/\").concat(year, \" \").concat(court);\n}\n/**\n * Validate Kenya ID number\n */ function validateKenyanID(id) {\n    const idRegex = /^\\d{8}$/;\n    return idRegex.test(id);\n}\n/**\n * Validate Kenya phone number\n */ function validateKenyanPhone(phone) {\n    const phoneRegex = /^(\\+254|0)[17]\\d{8}$/;\n    return phoneRegex.test(phone);\n}\n/**\n * Get case status color for UI\n */ function getCaseStatusColor(status) {\n    const statusColors = {\n        'PENDING': 'status-pending',\n        'ACTIVE': 'status-active',\n        'HEARING': 'status-active',\n        'JUDGMENT': 'status-active',\n        'COMPLETED': 'status-completed',\n        'DISMISSED': 'status-cancelled',\n        'WITHDRAWN': 'status-cancelled'\n    };\n    return statusColors[status] || 'status-pending';\n}\n/**\n * Calculate bail amount based on offense type (simplified)\n */ function calculateBailAmount(offenseType, offenseGravity) {\n    const baseAmounts = {\n        'THEFT': {\n            LOW: 10000,\n            MEDIUM: 25000,\n            HIGH: 50000\n        },\n        'ASSAULT': {\n            LOW: 15000,\n            MEDIUM: 30000,\n            HIGH: 75000\n        },\n        'FRAUD': {\n            LOW: 50000,\n            MEDIUM: 100000,\n            HIGH: 200000\n        },\n        'DRUG_OFFENSE': {\n            LOW: 20000,\n            MEDIUM: 50000,\n            HIGH: 100000\n        },\n        'TRAFFIC': {\n            LOW: 5000,\n            MEDIUM: 15000,\n            HIGH: 30000\n        },\n        'DEFAULT': {\n            LOW: 10000,\n            MEDIUM: 25000,\n            HIGH: 50000\n        }\n    };\n    const amounts = baseAmounts[offenseType] || baseAmounts.DEFAULT;\n    return amounts[offenseGravity];\n}\n/**\n * Get user-friendly error messages\n */ function getUserFriendlyError(error) {\n    const errorMessages = {\n        'NETWORK_ERROR': 'Please check your internet connection and try again.',\n        'VALIDATION_ERROR': 'Please check the information you entered and try again.',\n        'PERMISSION_ERROR': 'You do not have permission to perform this action.',\n        'NOT_FOUND': 'The requested information could not be found.',\n        'SERVER_ERROR': 'There was a problem with the system. Please try again later.',\n        'TIMEOUT': 'The request took too long. Please try again.'\n    };\n    return errorMessages[error] || 'An unexpected error occurred. Please try again.';\n}\n/**\n * Debounce function for search inputs\n */ function debounce(func, wait) {\n    let timeout;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Simple text search function\n */ function simpleSearch(items, searchTerm, searchFields) {\n    if (!searchTerm.trim()) return items;\n    const term = searchTerm.toLowerCase();\n    return items.filter((item)=>searchFields.some((field)=>{\n            const value = getNestedValue(item, field);\n            return value && value.toString().toLowerCase().includes(term);\n        }));\n}\n/**\n * Get nested object value by path\n */ function getNestedValue(obj, path) {\n    return path.split('.').reduce((current, key)=>current === null || current === void 0 ? void 0 : current[key], obj);\n}\n/**\n * Format file size for display\n */ function formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = [\n        'Bytes',\n        'KB',\n        'MB',\n        'GB'\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n/**\n * Check if user is on mobile device\n */ function isMobile() {\n    if (false) {}\n    return window.innerWidth < 768;\n}\n/**\n * Scroll to element smoothly\n */ function scrollToElement(elementId) {\n    const element = document.getElementById(elementId);\n    if (element) {\n        element.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n        });\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);