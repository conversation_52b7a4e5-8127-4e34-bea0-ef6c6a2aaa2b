/**
 * Case Stage Management Workflow System for KesiTrack
 * Automated stage progression tracking from Mention to Judgment
 */

export interface CaseStage {
  id: string
  name: string
  description: string
  order: number
  requirements: StageRequirement[]
  nextStages: string[]
  timeLimit?: number // days
  isRequired: boolean
}

export interface StageRequirement {
  id: string
  name: string
  description: string
  type: 'document' | 'action' | 'verification' | 'approval'
  isRequired: boolean
  isCompleted: boolean
  completedBy?: string
  completedAt?: string
  notes?: string
}

export interface CaseProgress {
  caseId: string
  caseNumber: string
  caseType: 'criminal' | 'civil' | 'family' | 'commercial'
  currentStage: string
  stageHistory: StageHistoryEntry[]
  requirements: StageRequirement[]
  nextActions: NextAction[]
  isReadyForNextStage: boolean
  overallProgress: number // percentage
  timelineStatus: 'on-track' | 'attention' | 'urgent' | 'overdue'
  lastUpdated: string
}

export interface StageHistoryEntry {
  stageId: string
  stageName: string
  enteredAt: string
  exitedAt?: string
  duration?: number // days
  completedBy: string
  notes?: string
}

export interface NextAction {
  id: string
  description: string
  assignedTo: string
  dueDate?: string
  priority: 'low' | 'medium' | 'high'
  category: 'document' | 'hearing' | 'administrative' | 'judicial'
}

/**
 * Case Stage Definitions for Different Case Types
 */
export const CRIMINAL_CASE_STAGES: CaseStage[] = [
  {
    id: 'filing',
    name: 'Case Filing',
    description: 'Initial case filing and registration',
    order: 1,
    requirements: [
      {
        id: 'charge-sheet',
        name: 'Charge Sheet Filed',
        description: 'Proper charge sheet filed with court',
        type: 'document',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'case-number',
        name: 'Case Number Assigned',
        description: 'Unique case number assigned by registry',
        type: 'action',
        isRequired: true,
        isCompleted: false
      }
    ],
    nextStages: ['first-mention'],
    timeLimit: 1,
    isRequired: true
  },
  {
    id: 'first-mention',
    name: 'First Mention',
    description: 'Initial court appearance for plea taking',
    order: 2,
    requirements: [
      {
        id: 'accused-present',
        name: 'Accused Present',
        description: 'Accused person present in court or represented',
        type: 'verification',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'charges-read',
        name: 'Charges Read',
        description: 'Charges read and explained to accused',
        type: 'action',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'plea-taken',
        name: 'Plea Taken',
        description: 'Accused plea recorded',
        type: 'action',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'legal-representation',
        name: 'Legal Representation',
        description: 'Legal representation confirmed or assigned',
        type: 'verification',
        isRequired: true,
        isCompleted: false
      }
    ],
    nextStages: ['pre-trial', 'trial'],
    timeLimit: 14,
    isRequired: true
  },
  {
    id: 'pre-trial',
    name: 'Pre-trial Conference',
    description: 'Pre-trial preparations and case management',
    order: 3,
    requirements: [
      {
        id: 'witness-list',
        name: 'Witness List Filed',
        description: 'Prosecution witness list filed',
        type: 'document',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'evidence-disclosure',
        name: 'Evidence Disclosure',
        description: 'Evidence disclosed to defense',
        type: 'action',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'trial-dates',
        name: 'Trial Dates Set',
        description: 'Trial dates scheduled and communicated',
        type: 'action',
        isRequired: true,
        isCompleted: false
      }
    ],
    nextStages: ['trial'],
    timeLimit: 30,
    isRequired: false
  },
  {
    id: 'trial',
    name: 'Trial',
    description: 'Main trial proceedings',
    order: 4,
    requirements: [
      {
        id: 'prosecution-case',
        name: 'Prosecution Case',
        description: 'Prosecution evidence presented',
        type: 'action',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'defense-case',
        name: 'Defense Case',
        description: 'Defense case presented (if any)',
        type: 'action',
        isRequired: false,
        isCompleted: false
      },
      {
        id: 'closing-arguments',
        name: 'Closing Arguments',
        description: 'Final submissions by both parties',
        type: 'action',
        isRequired: true,
        isCompleted: false
      }
    ],
    nextStages: ['judgment'],
    timeLimit: 60,
    isRequired: true
  },
  {
    id: 'judgment',
    name: 'Judgment',
    description: 'Court decision and sentencing',
    order: 5,
    requirements: [
      {
        id: 'judgment-written',
        name: 'Judgment Written',
        description: 'Written judgment prepared',
        type: 'document',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'judgment-delivered',
        name: 'Judgment Delivered',
        description: 'Judgment delivered in open court',
        type: 'action',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'sentence-imposed',
        name: 'Sentence Imposed',
        description: 'Sentence imposed if guilty verdict',
        type: 'action',
        isRequired: false,
        isCompleted: false
      }
    ],
    nextStages: ['closed'],
    timeLimit: 60,
    isRequired: true
  },
  {
    id: 'closed',
    name: 'Case Closed',
    description: 'Case concluded',
    order: 6,
    requirements: [
      {
        id: 'record-complete',
        name: 'Record Complete',
        description: 'Case record completed and filed',
        type: 'document',
        isRequired: true,
        isCompleted: false
      }
    ],
    nextStages: [],
    isRequired: true
  }
]

export const CIVIL_CASE_STAGES: CaseStage[] = [
  {
    id: 'filing',
    name: 'Case Filing',
    description: 'Initial case filing and service',
    order: 1,
    requirements: [
      {
        id: 'plaint-filed',
        name: 'Plaint Filed',
        description: 'Plaint properly filed with court fees',
        type: 'document',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'summons-issued',
        name: 'Summons Issued',
        description: 'Summons issued to defendant',
        type: 'action',
        isRequired: true,
        isCompleted: false
      }
    ],
    nextStages: ['pleadings'],
    timeLimit: 7,
    isRequired: true
  },
  {
    id: 'pleadings',
    name: 'Pleadings',
    description: 'Exchange of pleadings between parties',
    order: 2,
    requirements: [
      {
        id: 'defense-filed',
        name: 'Defense Filed',
        description: 'Written statement of defense filed',
        type: 'document',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'reply-filed',
        name: 'Reply Filed',
        description: 'Reply to defense filed (if necessary)',
        type: 'document',
        isRequired: false,
        isCompleted: false
      },
      {
        id: 'issues-framed',
        name: 'Issues Framed',
        description: 'Issues for trial framed by court',
        type: 'action',
        isRequired: true,
        isCompleted: false
      }
    ],
    nextStages: ['case-management'],
    timeLimit: 30,
    isRequired: true
  },
  {
    id: 'case-management',
    name: 'Case Management Conference',
    description: 'Case management and trial preparation',
    order: 3,
    requirements: [
      {
        id: 'witness-statements',
        name: 'Witness Statements',
        description: 'Witness statements exchanged',
        type: 'document',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'document-discovery',
        name: 'Document Discovery',
        description: 'Relevant documents disclosed',
        type: 'action',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'hearing-dates',
        name: 'Hearing Dates Set',
        description: 'Hearing dates scheduled',
        type: 'action',
        isRequired: true,
        isCompleted: false
      }
    ],
    nextStages: ['hearing'],
    timeLimit: 21,
    isRequired: true
  },
  {
    id: 'hearing',
    name: 'Hearing',
    description: 'Main hearing of the case',
    order: 4,
    requirements: [
      {
        id: 'plaintiff-evidence',
        name: 'Plaintiff Evidence',
        description: 'Plaintiff evidence presented',
        type: 'action',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'defendant-evidence',
        name: 'Defendant Evidence',
        description: 'Defendant evidence presented',
        type: 'action',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'final-submissions',
        name: 'Final Submissions',
        description: 'Final submissions by parties',
        type: 'action',
        isRequired: true,
        isCompleted: false
      }
    ],
    nextStages: ['judgment'],
    timeLimit: 45,
    isRequired: true
  },
  {
    id: 'judgment',
    name: 'Judgment',
    description: 'Court decision',
    order: 5,
    requirements: [
      {
        id: 'judgment-written',
        name: 'Judgment Written',
        description: 'Written judgment prepared',
        type: 'document',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'judgment-delivered',
        name: 'Judgment Delivered',
        description: 'Judgment delivered in open court',
        type: 'action',
        isRequired: true,
        isCompleted: false
      },
      {
        id: 'decree-drawn',
        name: 'Decree Drawn',
        description: 'Formal decree prepared and signed',
        type: 'document',
        isRequired: true,
        isCompleted: false
      }
    ],
    nextStages: ['closed'],
    timeLimit: 60,
    isRequired: true
  },
  {
    id: 'closed',
    name: 'Case Closed',
    description: 'Case concluded',
    order: 6,
    requirements: [
      {
        id: 'record-complete',
        name: 'Record Complete',
        description: 'Case record completed and archived',
        type: 'document',
        isRequired: true,
        isCompleted: false
      }
    ],
    nextStages: [],
    isRequired: true
  }
]

/**
 * Case Stage Management Engine
 */
export class CaseStageManager {
  /**
   * Get stage definitions for a case type
   */
  static getStagesForCaseType(caseType: string): CaseStage[] {
    switch (caseType.toLowerCase()) {
      case 'criminal':
        return CRIMINAL_CASE_STAGES
      case 'civil':
        return CIVIL_CASE_STAGES
      case 'family':
        // Family cases follow similar pattern to civil cases
        return CIVIL_CASE_STAGES.map(stage => ({
          ...stage,
          id: `family-${stage.id}`,
          name: stage.name.replace('Civil', 'Family')
        }))
      case 'commercial':
        // Commercial cases follow civil procedure with some modifications
        return CIVIL_CASE_STAGES.map(stage => ({
          ...stage,
          id: `commercial-${stage.id}`,
          timeLimit: stage.timeLimit ? Math.floor(stage.timeLimit * 0.8) : undefined // Faster timelines
        }))
      default:
        return CIVIL_CASE_STAGES
    }
  }

  /**
   * Initialize case progress tracking
   */
  static initializeCaseProgress(
    caseId: string,
    caseNumber: string,
    caseType: string,
    filingDate: string
  ): CaseProgress {
    const stages = this.getStagesForCaseType(caseType)
    const firstStage = stages[0]
    
    return {
      caseId,
      caseNumber,
      caseType: caseType as any,
      currentStage: firstStage.id,
      stageHistory: [{
        stageId: firstStage.id,
        stageName: firstStage.name,
        enteredAt: filingDate,
        completedBy: 'System'
      }],
      requirements: firstStage.requirements.map(req => ({ ...req })),
      nextActions: this.generateNextActions(firstStage, caseType),
      isReadyForNextStage: false,
      overallProgress: 0,
      timelineStatus: 'on-track',
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Update requirement completion
   */
  static updateRequirement(
    progress: CaseProgress,
    requirementId: string,
    isCompleted: boolean,
    completedBy: string,
    notes?: string
  ): CaseProgress {
    const updatedRequirements = progress.requirements.map(req =>
      req.id === requirementId
        ? {
            ...req,
            isCompleted,
            completedBy: isCompleted ? completedBy : undefined,
            completedAt: isCompleted ? new Date().toISOString() : undefined,
            notes
          }
        : req
    )

    const isReadyForNextStage = this.checkStageReadiness(progress.currentStage, progress.caseType, updatedRequirements)
    const overallProgress = this.calculateOverallProgress(progress.caseType, progress.currentStage, updatedRequirements)
    const timelineStatus = this.assessTimelineStatus(progress)

    return {
      ...progress,
      requirements: updatedRequirements,
      isReadyForNextStage,
      overallProgress,
      timelineStatus,
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Advance to next stage
   */
  static advanceToNextStage(
    progress: CaseProgress,
    nextStageId: string,
    completedBy: string,
    notes?: string
  ): CaseProgress {
    const stages = this.getStagesForCaseType(progress.caseType)
    const currentStage = stages.find(s => s.id === progress.currentStage)
    const nextStage = stages.find(s => s.id === nextStageId)

    if (!currentStage || !nextStage) {
      throw new Error('Invalid stage transition')
    }

    // Check if transition is allowed
    if (!currentStage.nextStages.includes(nextStageId)) {
      throw new Error(`Cannot advance from ${currentStage.name} to ${nextStage.name}`)
    }

    // Update stage history
    const updatedHistory = [...progress.stageHistory]
    const currentHistoryEntry = updatedHistory[updatedHistory.length - 1]
    if (currentHistoryEntry && !currentHistoryEntry.exitedAt) {
      currentHistoryEntry.exitedAt = new Date().toISOString()
      currentHistoryEntry.duration = Math.floor(
        (new Date().getTime() - new Date(currentHistoryEntry.enteredAt).getTime()) / (1000 * 60 * 60 * 24)
      )
    }

    updatedHistory.push({
      stageId: nextStageId,
      stageName: nextStage.name,
      enteredAt: new Date().toISOString(),
      completedBy,
      notes
    })

    const overallProgress = this.calculateOverallProgress(progress.caseType, nextStageId, nextStage.requirements)

    return {
      ...progress,
      currentStage: nextStageId,
      stageHistory: updatedHistory,
      requirements: nextStage.requirements.map(req => ({ ...req })),
      nextActions: this.generateNextActions(nextStage, progress.caseType),
      isReadyForNextStage: false,
      overallProgress,
      timelineStatus: this.assessTimelineStatus(progress),
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Check if current stage requirements are met
   */
  private static checkStageReadiness(
    currentStageId: string,
    caseType: string,
    requirements: StageRequirement[]
  ): boolean {
    const requiredRequirements = requirements.filter(req => req.isRequired)
    return requiredRequirements.every(req => req.isCompleted)
  }

  /**
   * Calculate overall case progress percentage
   */
  private static calculateOverallProgress(
    caseType: string,
    currentStageId: string,
    currentRequirements: StageRequirement[]
  ): number {
    const stages = this.getStagesForCaseType(caseType)
    const currentStageIndex = stages.findIndex(s => s.id === currentStageId)
    
    if (currentStageIndex === -1) return 0

    // Progress from completed stages
    const completedStagesProgress = (currentStageIndex / stages.length) * 100

    // Progress within current stage
    const totalRequirements = currentRequirements.length
    const completedRequirements = currentRequirements.filter(req => req.isCompleted).length
    const currentStageProgress = totalRequirements > 0 ? (completedRequirements / totalRequirements) * (100 / stages.length) : 0

    return Math.round(completedStagesProgress + currentStageProgress)
  }

  /**
   * Assess timeline status
   */
  private static assessTimelineStatus(progress: CaseProgress): 'on-track' | 'attention' | 'urgent' | 'overdue' {
    const stages = this.getStagesForCaseType(progress.caseType)
    const currentStage = stages.find(s => s.id === progress.currentStage)
    
    if (!currentStage?.timeLimit) return 'on-track'

    const currentStageEntry = progress.stageHistory[progress.stageHistory.length - 1]
    const daysInStage = Math.floor(
      (new Date().getTime() - new Date(currentStageEntry.enteredAt).getTime()) / (1000 * 60 * 60 * 24)
    )

    const timeLimit = currentStage.timeLimit
    
    if (daysInStage > timeLimit) return 'overdue'
    if (daysInStage > timeLimit * 0.8) return 'urgent'
    if (daysInStage > timeLimit * 0.6) return 'attention'
    return 'on-track'
  }

  /**
   * Generate next actions for a stage
   */
  private static generateNextActions(stage: CaseStage, caseType: string): NextAction[] {
    const actions: NextAction[] = []

    stage.requirements.forEach(req => {
      if (!req.isCompleted && req.isRequired) {
        actions.push({
          id: `action-${req.id}`,
          description: req.description,
          assignedTo: this.getDefaultAssignee(req.type, caseType),
          priority: req.isRequired ? 'high' : 'medium',
          category: this.mapRequirementTypeToCategory(req.type),
          dueDate: stage.timeLimit ? 
            new Date(Date.now() + stage.timeLimit * 24 * 60 * 60 * 1000).toISOString() : 
            undefined
        })
      }
    })

    return actions
  }

  private static getDefaultAssignee(requirementType: string, caseType: string): string {
    switch (requirementType) {
      case 'document':
        return 'Court Clerk'
      case 'action':
        return 'Magistrate'
      case 'verification':
        return 'Court Clerk'
      case 'approval':
        return 'Magistrate'
      default:
        return 'Court Staff'
    }
  }

  private static mapRequirementTypeToCategory(type: string): 'document' | 'hearing' | 'administrative' | 'judicial' {
    switch (type) {
      case 'document':
        return 'document'
      case 'action':
        return 'judicial'
      case 'verification':
        return 'administrative'
      case 'approval':
        return 'judicial'
      default:
        return 'administrative'
    }
  }

  /**
   * Get stage statistics for dashboard
   */
  static getStageStatistics(cases: CaseProgress[]): {
    totalCases: number
    byStage: Record<string, number>
    overdue: number
    onTrack: number
  } {
    const stats = {
      totalCases: cases.length,
      byStage: {} as Record<string, number>,
      overdue: 0,
      onTrack: 0
    }

    cases.forEach(case_ => {
      // Count by stage
      stats.byStage[case_.currentStage] = (stats.byStage[case_.currentStage] || 0) + 1
      
      // Count timeline status
      if (case_.timelineStatus === 'overdue') {
        stats.overdue++
      } else if (case_.timelineStatus === 'on-track') {
        stats.onTrack++
      }
    })

    return stats
  }
}
