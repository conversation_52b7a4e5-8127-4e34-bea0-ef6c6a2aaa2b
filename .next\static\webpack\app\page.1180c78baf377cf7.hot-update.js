"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const { user, logout, hasPermission } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    // Mock data for demonstration\n    const todaysCases = [\n        {\n            id: 1,\n            caseNumber: 'CR 45/2024 MC',\n            title: 'Republic vs John Doe',\n            type: 'Criminal',\n            status: 'HEARING',\n            time: '09:00 AM',\n            priority: 'HIGH'\n        },\n        {\n            id: 2,\n            caseNumber: 'CV 23/2024 MC',\n            title: 'Jane Smith vs ABC Company',\n            type: 'Civil',\n            status: 'MENTION',\n            time: '10:30 AM',\n            priority: 'MEDIUM'\n        },\n        {\n            id: 3,\n            caseNumber: 'CR 67/2024 MC',\n            title: 'Republic vs Mary Johnson',\n            type: 'Criminal',\n            status: 'JUDGMENT',\n            time: '02:00 PM',\n            priority: 'HIGH'\n        }\n    ];\n    const quickActions = [\n        {\n            title: 'Start New Case',\n            description: 'Begin a new case proceeding',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-blue-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>alert('Start New Case - Feature coming soon!')\n        },\n        {\n            title: 'View Court List',\n            description: 'See today\\'s scheduled cases',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-green-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>alert('Court List - Feature coming soon!')\n        },\n        {\n            title: 'Start Recording',\n            description: 'Begin court session transcription',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-red-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>alert('Recording - Feature coming soon!')\n        },\n        {\n            title: 'Draft Judgment',\n            description: 'Create or edit court judgments',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-purple-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>alert('Draft Judgment - Feature coming soon!')\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l-3-9m3 9l3-9\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"KesiTrack\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Legal Case Management System\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatLegalDate)(currentTime)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCourtTime)(currentTime)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"secondary\",\n                                                size: \"medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    (user === null || user === void 0 ? void 0 : user.displayName) || (user === null || user === void 0 ? void 0 : user.username)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"danger\",\n                                                size: \"medium\",\n                                                onClick: logout,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Sign Out\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Welcome to KesiTrack\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600\",\n                                children: \"Your AI-powered legal case management system for efficient court proceedings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Quick Actions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: quickActions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.QuickActionCard, {\n                                        title: action.title,\n                                        description: action.description,\n                                        icon: action.icon,\n                                        onClick: action.onClick\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: [\n                                    \"Today's Court List - \",\n                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatLegalDate)(new Date())\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: todaysCases.map((case_)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.StatusCard, {\n                                        title: \"\".concat(case_.caseNumber, \" - \").concat(case_.title),\n                                        status: case_.status,\n                                        description: \"\".concat(case_.type, \" case scheduled for \").concat(case_.time),\n                                        statusColor: case_.status === 'HEARING' ? 'status-active' : case_.status === 'JUDGMENT' ? 'status-pending' : 'status-pending',\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-blue-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"primary\",\n                                            size: \"small\",\n                                            children: \"Open Case\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    }, case_.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.InfoCard, {\n                                type: \"info\",\n                                title: \"System Status\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"All systems are operational. Transcription service is active and ready for court proceedings.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.InfoCard, {\n                                type: \"success\",\n                                title: \"Recent Updates\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Enhanced AI transcription accuracy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"New judgment templates added\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Improved case search functionality\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"FuTLB10cBDOXIDS2kg/K/Mm2N9E=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = Dashboard;\n// Export the component with authentication protection\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_c1 = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.withAuth)(Dashboard));\nvar _c, _c1;\n$RefreshReg$(_c, \"Dashboard\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});