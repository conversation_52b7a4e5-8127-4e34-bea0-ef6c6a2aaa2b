@import "tailwindcss";

/* Custom CSS Variables for KesiTrack Legal System */
:root {
  --background: #f8fafc;
  --foreground: #1e293b;
  --primary: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary: #64748b;
  --success: #059669;
  --warning: #d97706;
  --danger: #dc2626;
  --border: #e2e8f0;
  --card-bg: #ffffff;
}

/* Enhanced theme configuration for legal system */
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --font-sans: 'Inter', system-ui, -apple-system, sans-serif;
  --font-mono: 'JetBrains Mono', monospace;
}

/* Base styles for better accessibility and readability */
html {
  font-size: 18px; /* Larger base font size for judicial officers */
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  font-weight: 400;
}

/* High contrast focus indicators for accessibility */
*:focus {
  outline: 3px solid var(--primary);
  outline-offset: 2px;
}

/* Ensure minimum touch target size for tablets */
button, a, input, select, textarea {
  min-height: 44px;
}

/* Large, accessible button styles */
.btn-primary {
  background-color: var(--primary);
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-size: 1.125rem;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #f1f5f9;
  color: var(--secondary);
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-size: 1.125rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border);
  cursor: pointer;
}

.btn-secondary:hover {
  background-color: #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-success {
  background-color: var(--success);
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-size: 1.125rem;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;
}

.btn-success:hover {
  background-color: #047857;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.btn-warning {
  background-color: var(--warning);
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-size: 1.125rem;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;
}

.btn-warning:hover {
  background-color: #b45309;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.btn-danger {
  background-color: var(--danger);
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-size: 1.125rem;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;
}

.btn-danger:hover {
  background-color: #b91c1c;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Card styles for content sections */
.card {
  background-color: var(--card-bg);
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border);
  padding: 1.5rem;
}

.card-header {
  border-bottom: 1px solid var(--border);
  padding-bottom: 1rem;
  margin-bottom: 1.5rem;
}

/* Form input styles */
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1.125rem;
  border: 2px solid var(--border);
  border-radius: 0.75rem;
  transition: all 0.2s ease;
  background-color: white;
}

.form-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-label {
  display: block;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: 0.5rem;
}
