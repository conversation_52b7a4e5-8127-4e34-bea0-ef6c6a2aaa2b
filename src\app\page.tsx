'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardHeader, CardContent, QuickActionCard, StatusCard, InfoCard } from '@/components/ui/Card'
import { formatLegalDate, formatCourtTime } from '@/lib/utils'
import { useAuth, withAuth } from '@/contexts/AuthContext'

function Dashboard() {
  const [currentTime, setCurrentTime] = useState(new Date())
  const { user, logout, hasPermission } = useAuth()

  // Mock data for demonstration
  const todaysCases = [
    {
      id: 1,
      caseNumber: 'CR 45/2024 MC',
      title: '<PERSON> vs <PERSON>',
      type: 'Criminal',
      status: 'HEARING',
      time: '09:00 AM',
      priority: 'HIGH'
    },
    {
      id: 2,
      caseNumber: 'CV 23/2024 MC',
      title: '<PERSON> vs ABC Company',
      type: 'Civil',
      status: 'MENTION',
      time: '10:30 AM',
      priority: 'MEDIUM'
    },
    {
      id: 3,
      caseNumber: 'CR 67/2024 MC',
      title: '<PERSON> vs <PERSON>',
      type: 'Criminal',
      status: 'JUDGMENT',
      time: '02:00 PM',
      priority: 'HIGH'
    }
  ]

  // Role-based quick actions
  const allQuickActions = [
    {
      title: 'Start New Case',
      description: 'Begin a new case proceeding',
      permission: 'create_case',
      icon: (
        <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      ),
      onClick: () => alert('Start New Case - Feature coming soon!')
    },
    {
      title: 'View Court List',
      description: 'See today\'s scheduled cases',
      permission: 'manage_court_list',
      icon: (
        <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
      ),
      onClick: () => window.location.href = '/court-list'
    },
    {
      title: 'Start Recording',
      description: 'Begin court session transcription',
      permission: 'start_recording',
      icon: (
        <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
        </svg>
      ),
      onClick: () => window.location.href = '/transcription'
    },
    {
      title: 'Draft Judgment',
      description: 'Create or edit court judgments',
      permission: 'draft_judgment',
      icon: (
        <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
      ),
      onClick: () => window.location.href = '/judgment-drafting'
    },
    {
      title: 'View My Cases',
      description: 'See cases assigned to you',
      permission: 'view_own_cases',
      icon: (
        <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
      ),
      onClick: () => alert('My Cases - Feature coming soon!')
    },
    {
      title: 'AI Case Analysis',
      description: 'Get AI-powered case insights and recommendations',
      permission: 'view_all_cases',
      icon: (
        <svg className="w-8 h-8 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      onClick: () => window.location.href = '/case-analysis'
    },
    {
      title: 'Kenya Law Reports',
      description: 'Search case law and legal precedents',
      permission: 'view_all_cases',
      icon: (
        <svg className="w-8 h-8 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      ),
      onClick: () => window.location.href = '/case-law'
    }
  ]

  // Filter actions based on user permissions
  const quickActions = allQuickActions.filter(action => hasPermission(action.permission))

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l-3-9m3 9l3-9" />
                  </svg>
                </div>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">KesiTrack</h1>
                <p className="text-sm text-gray-600">Legal Case Management System</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-lg font-semibold text-gray-900">
                  {formatLegalDate(currentTime)}
                </p>
                <p className="text-sm text-gray-600">
                  {formatCourtTime(currentTime)}
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <Button variant="secondary" size="medium">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  {user?.displayName || user?.username}
                </Button>
                <Button variant="danger" size="medium" onClick={logout}>
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  Sign Out
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome to KesiTrack
          </h2>
          <p className="text-lg text-gray-600">
            Your AI-powered legal case management system for efficient court proceedings
          </p>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <QuickActionCard
                key={index}
                title={action.title}
                description={action.description}
                icon={action.icon}
                onClick={action.onClick}
              />
            ))}
          </div>
        </div>

        {/* Today's Cases */}
        <div className="mb-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            Today's Court List - {formatLegalDate(new Date())}
          </h3>
          <div className="space-y-4">
            {todaysCases.map((case_) => (
              <StatusCard
                key={case_.id}
                title={`${case_.caseNumber} - ${case_.title}`}
                status={case_.status}
                description={`${case_.type} case scheduled for ${case_.time}`}
                statusColor={
                  case_.status === 'HEARING' ? 'status-active' :
                  case_.status === 'JUDGMENT' ? 'status-pending' :
                  'status-pending'
                }
                icon={
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                }
                actions={
                  <Button variant="primary" size="small">
                    Open Case
                  </Button>
                }
              />
            ))}
          </div>
        </div>

        {/* System Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <InfoCard type="info" title="System Status">
            <p>All systems are operational. Transcription service is active and ready for court proceedings.</p>
          </InfoCard>

          <InfoCard type="success" title="Recent Updates">
            <ul className="list-disc list-inside space-y-1">
              <li>Enhanced AI transcription accuracy</li>
              <li>New judgment templates added</li>
              <li>Improved case search functionality</li>
            </ul>
          </InfoCard>
        </div>
      </main>
    </div>
  )
}

// Export the component with authentication protection
export default withAuth(Dashboard)
