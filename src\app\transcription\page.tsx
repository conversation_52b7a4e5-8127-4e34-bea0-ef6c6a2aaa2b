'use client'

import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardHeader, CardContent, InfoCard } from '@/components/ui/Card'
import { FormField, Label, Select, Input } from '@/components/ui/Form'
import { formatCourtTime } from '@/lib/utils'
import { useAuth, withAuth } from '@/contexts/AuthContext'

interface TranscriptEntry {
  id: number
  timestamp: string
  speaker: string
  text: string
  confidence: number
}

interface Speaker {
  id: string
  name: string
  role: 'magistrate' | 'advocate' | 'witness' | 'clerk' | 'other'
  color: string
}

function TranscriptionPage() {
  const { user } = useAuth()
  const [isRecording, setIsRecording] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [currentCase, setCurrentCase] = useState('')
  const [transcript, setTranscript] = useState<TranscriptEntry[]>([])
  const [speakers, setSpeakers] = useState<Speaker[]>([
    { id: 'magistrate', name: 'Magistrate', role: 'magistrate', color: 'bg-blue-100 text-blue-800' },
    { id: 'prosecutor', name: 'Prosecutor', role: 'advocate', color: 'bg-red-100 text-red-800' },
    { id: 'defense', name: 'Defense Counsel', role: 'advocate', color: 'bg-green-100 text-green-800' },
    { id: 'witness', name: 'Witness', role: 'witness', color: 'bg-yellow-100 text-yellow-800' },
    { id: 'clerk', name: 'Court Clerk', role: 'clerk', color: 'bg-purple-100 text-purple-800' }
  ])
  const [selectedSpeaker, setSelectedSpeaker] = useState('magistrate')
  const [recordingDuration, setRecordingDuration] = useState(0)
  const [editingEntry, setEditingEntry] = useState<number | null>(null)
  const [editText, setEditText] = useState('')
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const transcriptEndRef = useRef<HTMLDivElement>(null)

  // Mock cases for demonstration
  const cases = [
    { value: 'CR-45-2024', label: 'CR 45/2024 MC - Republic vs John Doe' },
    { value: 'CV-23-2024', label: 'CV 23/2024 MC - Jane Smith vs ABC Company' },
    { value: 'CR-67-2024', label: 'CR 67/2024 MC - Republic vs Mary Johnson' }
  ]

  useEffect(() => {
    if (isRecording && !isPaused) {
      intervalRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1)
      }, 1000)
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isRecording, isPaused])

  useEffect(() => {
    // Auto-scroll to bottom when new transcript entries are added
    transcriptEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [transcript])

  const startRecording = () => {
    if (!currentCase) {
      alert('Please select a case before starting recording')
      return
    }
    
    setIsRecording(true)
    setIsPaused(false)
    setRecordingDuration(0)
    
    // Add initial entry
    const initialEntry: TranscriptEntry = {
      id: Date.now(),
      timestamp: formatCourtTime(new Date()),
      speaker: 'System',
      text: `Recording started for case ${currentCase}`,
      confidence: 1.0
    }
    setTranscript([initialEntry])
    
    // Simulate real-time transcription
    simulateTranscription()
  }

  const pauseRecording = () => {
    setIsPaused(true)
    addSystemMessage('Recording paused')
  }

  const resumeRecording = () => {
    setIsPaused(false)
    addSystemMessage('Recording resumed')
    simulateTranscription()
  }

  const stopRecording = () => {
    setIsRecording(false)
    setIsPaused(false)
    addSystemMessage('Recording stopped')
  }

  const addSystemMessage = (message: string) => {
    const entry: TranscriptEntry = {
      id: Date.now(),
      timestamp: formatCourtTime(new Date()),
      speaker: 'System',
      text: message,
      confidence: 1.0
    }
    setTranscript(prev => [...prev, entry])
  }

  const simulateTranscription = () => {
    // Simulate AI transcription with mock data
    const mockPhrases = [
      'Your Honor, I would like to present evidence in this matter.',
      'The defendant was present at the scene on the date in question.',
      'I object to this line of questioning, Your Honor.',
      'The witness may answer the question.',
      'Please state your full name for the record.',
      'I understand the charges against me.',
      'The prosecution rests, Your Honor.',
      'Defense calls the next witness.',
      'This court is now in session.',
      'Please rise for the honorable magistrate.'
    ]

    const addMockEntry = () => {
      if (isRecording && !isPaused) {
        const randomPhrase = mockPhrases[Math.floor(Math.random() * mockPhrases.length)]
        const randomSpeaker = speakers[Math.floor(Math.random() * speakers.length)]
        
        const entry: TranscriptEntry = {
          id: Date.now(),
          timestamp: formatCourtTime(new Date()),
          speaker: randomSpeaker.name,
          text: randomPhrase,
          confidence: 0.85 + Math.random() * 0.15 // Random confidence between 0.85-1.0
        }
        
        setTranscript(prev => [...prev, entry])
        
        // Schedule next entry
        setTimeout(addMockEntry, 3000 + Math.random() * 7000) // Random interval 3-10 seconds
      }
    }

    // Start the simulation
    setTimeout(addMockEntry, 2000)
  }

  const addManualEntry = () => {
    const text = prompt('Enter text manually:')
    if (text && text.trim()) {
      const speaker = speakers.find(s => s.id === selectedSpeaker)
      const entry: TranscriptEntry = {
        id: Date.now(),
        timestamp: formatCourtTime(new Date()),
        speaker: speaker?.name || 'Unknown',
        text: text.trim(),
        confidence: 1.0
      }
      setTranscript(prev => [...prev, entry])
    }
  }

  const startEdit = (entry: TranscriptEntry) => {
    setEditingEntry(entry.id)
    setEditText(entry.text)
  }

  const saveEdit = () => {
    if (editingEntry) {
      setTranscript(prev => prev.map(entry => 
        entry.id === editingEntry 
          ? { ...entry, text: editText }
          : entry
      ))
      setEditingEntry(null)
      setEditText('')
    }
  }

  const cancelEdit = () => {
    setEditingEntry(null)
    setEditText('')
  }

  const deleteEntry = (entryId: number) => {
    if (confirm('Are you sure you want to delete this entry?')) {
      setTranscript(prev => prev.filter(entry => entry.id !== entryId))
    }
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  const exportTranscript = () => {
    const content = transcript.map(entry => 
      `[${entry.timestamp}] ${entry.speaker}: ${entry.text}`
    ).join('\n')
    
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `transcript-${currentCase}-${new Date().toISOString().split('T')[0]}.txt`
    a.click()
    URL.revokeObjectURL(url)
  }

  const getSpeakerColor = (speakerName: string) => {
    const speaker = speakers.find(s => s.name === speakerName)
    return speaker?.color || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button 
                variant="secondary" 
                onClick={() => window.history.back()}
                className="flex items-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Back to Dashboard
              </Button>
              <h1 className="text-2xl font-bold text-gray-900">Court Transcription</h1>
              {isRecording && (
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                  <span className="text-red-600 font-semibold">RECORDING</span>
                  <span className="text-gray-600">{formatDuration(recordingDuration)}</span>
                </div>
              )}
            </div>
            <div className="text-right">
              <p className="text-lg font-semibold text-gray-900">
                {formatCourtTime(new Date())}
              </p>
              <p className="text-sm text-gray-600">
                {user?.displayName}
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Controls Panel */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">Recording Controls</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <FormField>
                    <Label htmlFor="case">Select Case</Label>
                    <Select
                      id="case"
                      value={currentCase}
                      onChange={(e) => setCurrentCase(e.target.value)}
                      options={cases}
                      placeholder="Choose a case..."
                      disabled={isRecording}
                    />
                  </FormField>

                  <FormField>
                    <Label htmlFor="speaker">Current Speaker</Label>
                    <Select
                      id="speaker"
                      value={selectedSpeaker}
                      onChange={(e) => setSelectedSpeaker(e.target.value)}
                      options={speakers.map(s => ({ value: s.id, label: s.name }))}
                    />
                  </FormField>

                  {/* Recording Controls */}
                  <div className="space-y-3">
                    {!isRecording ? (
                      <Button
                        variant="success"
                        size="large"
                        onClick={startRecording}
                        className="w-full"
                        disabled={!currentCase}
                      >
                        <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                        </svg>
                        Start Recording
                      </Button>
                    ) : (
                      <div className="space-y-2">
                        {!isPaused ? (
                          <Button
                            variant="warning"
                            size="large"
                            onClick={pauseRecording}
                            className="w-full"
                          >
                            <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6" />
                            </svg>
                            Pause Recording
                          </Button>
                        ) : (
                          <Button
                            variant="success"
                            size="large"
                            onClick={resumeRecording}
                            className="w-full"
                          >
                            <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Resume Recording
                          </Button>
                        )}
                        <Button
                          variant="danger"
                          size="large"
                          onClick={stopRecording}
                          className="w-full"
                        >
                          <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10h6v4H9z" />
                          </svg>
                          Stop Recording
                        </Button>
                      </div>
                    )}

                    <Button
                      variant="secondary"
                      size="medium"
                      onClick={addManualEntry}
                      className="w-full"
                      disabled={!isRecording}
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Add Manual Entry
                    </Button>

                    {transcript.length > 0 && (
                      <Button
                        variant="primary"
                        size="medium"
                        onClick={exportTranscript}
                        className="w-full"
                      >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Export Transcript
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recording Stats */}
            {isRecording && (
              <Card className="mt-4">
                <CardHeader>
                  <h3 className="text-lg font-semibold text-gray-900">Session Stats</h3>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Duration:</span>
                      <span className="font-semibold">{formatDuration(recordingDuration)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Entries:</span>
                      <span className="font-semibold">{transcript.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <span className={`font-semibold ${isPaused ? 'text-yellow-600' : 'text-green-600'}`}>
                        {isPaused ? 'Paused' : 'Active'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Transcript Display */}
          <div className="lg:col-span-2">
            <Card className="h-[600px] flex flex-col">
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">Live Transcript</h3>
                {currentCase && (
                  <p className="text-sm text-gray-600">Case: {currentCase}</p>
                )}
              </CardHeader>
              <CardContent className="flex-1 overflow-hidden">
                <div className="h-full overflow-y-auto space-y-3">
                  {transcript.length === 0 ? (
                    <div className="flex items-center justify-center h-full text-gray-500">
                      <div className="text-center">
                        <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                        </svg>
                        <p className="text-lg">No transcript yet</p>
                        <p className="text-sm">Start recording to begin transcription</p>
                      </div>
                    </div>
                  ) : (
                    transcript.map(entry => (
                      <div key={entry.id} className="border-l-4 border-gray-200 pl-4 py-2">
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-gray-500">{entry.timestamp}</span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSpeakerColor(entry.speaker)}`}>
                              {entry.speaker}
                            </span>
                            {entry.confidence < 0.9 && entry.speaker !== 'System' && (
                              <span className="text-xs text-yellow-600">Low confidence</span>
                            )}
                          </div>
                          {entry.speaker !== 'System' && (
                            <div className="flex items-center space-x-1">
                              <button
                                onClick={() => startEdit(entry)}
                                className="text-blue-600 hover:text-blue-800 text-xs"
                              >
                                Edit
                              </button>
                              <button
                                onClick={() => deleteEntry(entry.id)}
                                className="text-red-600 hover:text-red-800 text-xs"
                              >
                                Delete
                              </button>
                            </div>
                          )}
                        </div>
                        {editingEntry === entry.id ? (
                          <div className="space-y-2">
                            <textarea
                              value={editText}
                              onChange={(e) => setEditText(e.target.value)}
                              className="w-full p-2 border border-gray-300 rounded text-sm"
                              rows={3}
                            />
                            <div className="flex space-x-2">
                              <Button variant="success" size="small" onClick={saveEdit}>
                                Save
                              </Button>
                              <Button variant="secondary" size="small" onClick={cancelEdit}>
                                Cancel
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <p className="text-gray-800">{entry.text}</p>
                        )}
                      </div>
                    ))
                  )}
                  <div ref={transcriptEndRef} />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Help Information */}
        <div className="mt-6">
          <InfoCard type="info" title="Transcription Tips">
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Ensure good audio quality by speaking clearly and minimizing background noise</li>
              <li>Select the correct speaker before they begin talking for better organization</li>
              <li>Use manual entry for important statements that need to be recorded accurately</li>
              <li>Review and edit the transcript regularly during breaks in proceedings</li>
              <li>Export the transcript at the end of each session for record keeping</li>
            </ul>
          </InfoCard>
        </div>
      </main>
    </div>
  )
}

// Export with authentication protection
export default withAuth(TranscriptionPage, ['start_recording'])
