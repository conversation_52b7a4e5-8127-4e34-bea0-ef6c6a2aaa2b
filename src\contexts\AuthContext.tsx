'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

export interface User {
  username: string
  role: 'magistrate' | 'clerk' | 'advocate'
  loginTime: string
  displayName?: string
}

interface AuthContextType {
  user: User | null
  login: (username: string, password: string, role: string) => Promise<boolean>
  logout: () => void
  isLoading: boolean
  hasPermission: (permission: string) => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Role-based permissions
const PERMISSIONS = {
  magistrate: [
    'view_all_cases',
    'create_case',
    'edit_case',
    'delete_case',
    'start_recording',
    'draft_judgment',
    'finalize_judgment',
    'manage_court_list',
    'view_analytics',
    'manage_templates',
    'approve_documents'
  ],
  clerk: [
    'view_assigned_cases',
    'create_case',
    'edit_case',
    'manage_court_list',
    'draft_documents',
    'prepare_templates',
    'schedule_hearings'
  ],
  advocate: [
    'view_own_cases',
    'view_case_status',
    'view_documents',
    'view_schedules'
  ]
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check for existing session on mount
    const checkExistingSession = () => {
      try {
        const storedUser = localStorage.getItem('kesitrack_user')
        if (storedUser) {
          const userData = JSON.parse(storedUser)
          // Check if session is still valid (24 hours)
          const loginTime = new Date(userData.loginTime)
          const now = new Date()
          const hoursDiff = (now.getTime() - loginTime.getTime()) / (1000 * 60 * 60)
          
          if (hoursDiff < 24) {
            setUser(userData)
          } else {
            // Session expired
            localStorage.removeItem('kesitrack_user')
          }
        }
      } catch (error) {
        console.error('Error checking session:', error)
        localStorage.removeItem('kesitrack_user')
      } finally {
        setIsLoading(false)
      }
    }

    checkExistingSession()
  }, [])

  const login = async (username: string, password: string, role: string): Promise<boolean> => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // For demo purposes, accept any credentials
      // In real implementation, this would validate against backend
      const userData: User = {
        username,
        role: role as User['role'],
        loginTime: new Date().toISOString(),
        displayName: getDisplayName(username, role as User['role'])
      }
      
      localStorage.setItem('kesitrack_user', JSON.stringify(userData))
      setUser(userData)
      return true
    } catch (error) {
      console.error('Login error:', error)
      return false
    }
  }

  const logout = () => {
    localStorage.removeItem('kesitrack_user')
    setUser(null)
    window.location.href = '/login'
  }

  const hasPermission = (permission: string): boolean => {
    if (!user) return false
    return PERMISSIONS[user.role]?.includes(permission) || false
  }

  const getDisplayName = (username: string, role: User['role']): string => {
    const roleNames = {
      magistrate: 'Magistrate',
      clerk: 'Court Clerk',
      advocate: 'Advocate'
    }
    
    // In real implementation, this would come from user database
    return `${roleNames[role]} ${username}`
  }

  const value = {
    user,
    login,
    logout,
    isLoading,
    hasPermission
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Higher-order component for protecting routes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredPermissions?: string[]
) {
  return function AuthenticatedComponent(props: P) {
    const { user, isLoading, hasPermission } = useAuth()

    if (isLoading) {
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg className="w-10 h-10 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Loading KesiTrack...
            </h2>
            <p className="text-gray-600">
              Please wait while we verify your session
            </p>
          </div>
        </div>
      )
    }

    if (!user) {
      window.location.href = '/login'
      return null
    }

    // Check permissions if required
    if (requiredPermissions && requiredPermissions.length > 0) {
      const hasRequiredPermissions = requiredPermissions.every(permission => 
        hasPermission(permission)
      )
      
      if (!hasRequiredPermissions) {
        return (
          <div className="min-h-screen bg-gray-50 flex items-center justify-center">
            <div className="text-center max-w-md">
              <div className="w-16 h-16 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Access Denied
              </h2>
              <p className="text-gray-600 mb-4">
                You do not have permission to access this feature. Please contact your administrator if you believe this is an error.
              </p>
              <button
                onClick={() => window.history.back()}
                className="btn-secondary"
              >
                Go Back
              </button>
            </div>
          </div>
        )
      }
    }

    return <Component {...props} />
  }
}
