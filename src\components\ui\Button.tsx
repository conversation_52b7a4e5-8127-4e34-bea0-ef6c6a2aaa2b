import React from 'react'
import { cn } from '@/lib/utils'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  size?: 'small' | 'medium' | 'large'
  loading?: boolean
  icon?: React.ReactNode
  children: React.ReactNode
}

/**
 * Large, accessible button component designed for non-technical users
 * Features:
 * - Large touch targets (minimum 44px)
 * - Clear visual feedback
 * - Loading states
 * - High contrast colors
 * - Keyboard navigation support
 */
export function Button({
  variant = 'primary',
  size = 'medium',
  loading = false,
  icon,
  children,
  className,
  disabled,
  ...props
}: ButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed'
  
  const variantClasses = {
    primary: 'btn-primary focus:ring-blue-300',
    secondary: 'btn-secondary focus:ring-gray-300',
    success: 'btn-success focus:ring-green-300',
    warning: 'btn-warning focus:ring-yellow-300',
    danger: 'btn-danger focus:ring-red-300',
  }
  
  const sizeClasses = {
    small: 'px-4 py-2 text-base min-h-[40px]',
    medium: 'px-6 py-3 text-lg min-h-[48px]',
    large: 'px-8 py-4 text-xl min-h-[56px]',
  }
  
  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <svg
          className="animate-spin -ml-1 mr-3 h-5 w-5"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {icon && !loading && <span className="mr-2">{icon}</span>}
      {children}
    </button>
  )
}

/**
 * Icon-only button for actions like edit, delete, etc.
 */
export function IconButton({
  variant = 'secondary',
  size = 'medium',
  loading = false,
  icon,
  className,
  disabled,
  'aria-label': ariaLabel,
  ...props
}: Omit<ButtonProps, 'children'> & { 'aria-label': string }) {
  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed'
  
  const variantClasses = {
    primary: 'btn-primary focus:ring-blue-300',
    secondary: 'btn-secondary focus:ring-gray-300',
    success: 'btn-success focus:ring-green-300',
    warning: 'btn-warning focus:ring-yellow-300',
    danger: 'btn-danger focus:ring-red-300',
  }
  
  const sizeClasses = {
    small: 'p-2 min-h-[40px] min-w-[40px]',
    medium: 'p-3 min-h-[48px] min-w-[48px]',
    large: 'p-4 min-h-[56px] min-w-[56px]',
  }
  
  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      disabled={disabled || loading}
      aria-label={ariaLabel}
      {...props}
    >
      {loading ? (
        <svg
          className="animate-spin h-5 w-5"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      ) : (
        icon
      )}
    </button>
  )
}

/**
 * Button group for related actions
 */
export function ButtonGroup({
  children,
  className,
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <div className={cn('flex flex-wrap gap-3', className)}>
      {children}
    </div>
  )
}
