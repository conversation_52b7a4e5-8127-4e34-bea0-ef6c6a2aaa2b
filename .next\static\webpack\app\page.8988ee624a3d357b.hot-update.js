"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const { user, logout, hasPermission } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    // Mock data for demonstration\n    const todaysCases = [\n        {\n            id: 1,\n            caseNumber: 'CR 45/2024 MC',\n            title: 'Republic vs John Doe',\n            type: 'Criminal',\n            status: 'HEARING',\n            time: '09:00 AM',\n            priority: 'HIGH'\n        },\n        {\n            id: 2,\n            caseNumber: 'CV 23/2024 MC',\n            title: 'Jane Smith vs ABC Company',\n            type: 'Civil',\n            status: 'MENTION',\n            time: '10:30 AM',\n            priority: 'MEDIUM'\n        },\n        {\n            id: 3,\n            caseNumber: 'CR 67/2024 MC',\n            title: 'Republic vs Mary Johnson',\n            type: 'Criminal',\n            status: 'JUDGMENT',\n            time: '02:00 PM',\n            priority: 'HIGH'\n        }\n    ];\n    // Role-based quick actions\n    const allQuickActions = [\n        {\n            title: 'Start New Case',\n            description: 'Begin a new case proceeding',\n            permission: 'create_case',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-blue-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>alert('Start New Case - Feature coming soon!')\n        },\n        {\n            title: 'View Court List',\n            description: 'See today\\'s scheduled cases',\n            permission: 'manage_court_list',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-green-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>window.location.href = '/court-list'\n        },\n        {\n            title: 'Start Recording',\n            description: 'Begin court session transcription',\n            permission: 'start_recording',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-red-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>window.location.href = '/transcription'\n        },\n        {\n            title: 'Draft Judgment',\n            description: 'Create or edit court judgments',\n            permission: 'draft_judgment',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-purple-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>window.location.href = '/documents'\n        },\n        {\n            title: 'View My Cases',\n            description: 'See cases assigned to you',\n            permission: 'view_own_cases',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-indigo-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>alert('My Cases - Feature coming soon!')\n        },\n        {\n            title: 'AI Case Analysis',\n            description: 'Get AI-powered case insights and recommendations',\n            permission: 'view_all_cases',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-cyan-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 107,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>window.location.href = '/case-analysis'\n        },\n        {\n            title: 'Kenya Law Reports',\n            description: 'Search case law and legal precedents',\n            permission: 'view_all_cases',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-amber-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this),\n            onClick: ()=>window.location.href = '/case-law'\n        }\n    ];\n    // Filter actions based on user permissions\n    const quickActions = allQuickActions.filter((action)=>hasPermission(action.permission));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l-3-9m3 9l3-9\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"KesiTrack\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Legal Case Management System\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatLegalDate)(currentTime)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCourtTime)(currentTime)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"secondary\",\n                                                size: \"medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    (user === null || user === void 0 ? void 0 : user.displayName) || (user === null || user === void 0 ? void 0 : user.username)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"danger\",\n                                                size: \"medium\",\n                                                onClick: logout,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Sign Out\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Welcome to KesiTrack\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600\",\n                                children: \"Your AI-powered legal case management system for efficient court proceedings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Quick Actions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: quickActions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.QuickActionCard, {\n                                        title: action.title,\n                                        description: action.description,\n                                        icon: action.icon,\n                                        onClick: action.onClick\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: [\n                                    \"Today's Court List - \",\n                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatLegalDate)(new Date())\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: todaysCases.map((case_)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.StatusCard, {\n                                        title: \"\".concat(case_.caseNumber, \" - \").concat(case_.title),\n                                        status: case_.status,\n                                        description: \"\".concat(case_.type, \" case scheduled for \").concat(case_.time),\n                                        statusColor: case_.status === 'HEARING' ? 'status-active' : case_.status === 'JUDGMENT' ? 'status-pending' : 'status-pending',\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-blue-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"primary\",\n                                            size: \"small\",\n                                            children: \"Open Case\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    }, case_.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.InfoCard, {\n                                type: \"info\",\n                                title: \"System Status\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"All systems are operational. Transcription service is active and ready for court proceedings.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.InfoCard, {\n                                type: \"success\",\n                                title: \"Recent Updates\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Enhanced AI transcription accuracy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"New judgment templates added\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Improved case search functionality\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"FuTLB10cBDOXIDS2kg/K/Mm2N9E=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = Dashboard;\n// Export the component with authentication protection\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_c1 = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.withAuth)(Dashboard));\nvar _c, _c1;\n$RefreshReg$(_c, \"Dashboard\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});