'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Card, CardHeader, CardContent, StatusCard, InfoCard } from '@/components/ui/Card'
import { FormField, Label, Select } from '@/components/ui/Form'
import { formatLegalDate, formatCourtTime, getCaseStatusColor } from '@/lib/utils'
import { useAuth, withAuth } from '@/contexts/AuthContext'

interface Case {
  id: number
  caseNumber: string
  title: string
  type: 'Criminal' | 'Civil' | 'Family' | 'Commercial'
  status: 'PENDING' | 'ACTIVE' | 'HEARING' | 'JUDGMENT' | 'COMPLETED'
  scheduledTime: string
  priority: 'LOW' | 'MEDIUM' | 'HIGH'
  advocates: string[]
  estimatedDuration: number // in minutes
  notes?: string
}

function CourtListPage() {
  const { user, hasPermission } = useAuth()
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [selectedCourtroom, setSelectedCourtroom] = useState('courtroom-1')
  const [draggedCase, setDraggedCase] = useState<Case | null>(null)

  // Mock data for demonstration
  const [cases, setCases] = useState<Case[]>([
    {
      id: 1,
      caseNumber: 'CR 45/2024 MC',
      title: 'Republic vs John Doe',
      type: 'Criminal',
      status: 'HEARING',
      scheduledTime: '09:00',
      priority: 'HIGH',
      advocates: ['Adv. Jane Smith', 'Adv. Peter Mwangi'],
      estimatedDuration: 60,
      notes: 'Bail application hearing'
    },
    {
      id: 2,
      caseNumber: 'CV 23/2024 MC',
      title: 'Jane Smith vs ABC Company',
      type: 'Civil',
      status: 'PENDING',
      scheduledTime: '10:30',
      priority: 'MEDIUM',
      advocates: ['Adv. Mary Wanjiku'],
      estimatedDuration: 45
    },
    {
      id: 3,
      caseNumber: 'CR 67/2024 MC',
      title: 'Republic vs Mary Johnson',
      type: 'Criminal',
      status: 'JUDGMENT',
      scheduledTime: '14:00',
      priority: 'HIGH',
      advocates: ['Adv. James Kiprotich'],
      estimatedDuration: 30,
      notes: 'Judgment delivery'
    },
    {
      id: 4,
      caseNumber: 'FM 12/2024 MC',
      title: 'In the matter of custody of minor XYZ',
      type: 'Family',
      status: 'PENDING',
      scheduledTime: '11:30',
      priority: 'MEDIUM',
      advocates: ['Adv. Grace Njeri', 'Adv. David Ochieng'],
      estimatedDuration: 90
    }
  ])

  const courtrooms = [
    { value: 'courtroom-1', label: 'Courtroom 1 - Main Hall' },
    { value: 'courtroom-2', label: 'Courtroom 2 - Family Court' },
    { value: 'courtroom-3', label: 'Courtroom 3 - Commercial Court' }
  ]

  const timeSlots = [
    '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00',
    '14:00', '14:30', '15:00', '15:30', '16:00', '16:30'
  ]

  const handleDragStart = (case_: Case) => {
    setDraggedCase(case_)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent, newTime: string) => {
    e.preventDefault()
    if (draggedCase) {
      setCases(prev => prev.map(case_ => 
        case_.id === draggedCase.id 
          ? { ...case_, scheduledTime: newTime }
          : case_
      ))
      setDraggedCase(null)
    }
  }

  const updateCaseStatus = (caseId: number, newStatus: Case['status']) => {
    setCases(prev => prev.map(case_ => 
      case_.id === caseId 
        ? { ...case_, status: newStatus }
        : case_
    ))
  }

  const getCasesByTimeSlot = (timeSlot: string) => {
    return cases.filter(case_ => case_.scheduledTime === timeSlot)
  }

  const getPriorityColor = (priority: Case['priority']) => {
    switch (priority) {
      case 'HIGH': return 'border-l-red-500 bg-red-50'
      case 'MEDIUM': return 'border-l-yellow-500 bg-yellow-50'
      case 'LOW': return 'border-l-green-500 bg-green-50'
      default: return 'border-l-gray-500 bg-gray-50'
    }
  }

  const getTypeIcon = (type: Case['type']) => {
    switch (type) {
      case 'Criminal':
        return (
          <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        )
      case 'Civil':
        return (
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        )
      case 'Family':
        return (
          <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        )
      case 'Commercial':
        return (
          <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button 
                variant="secondary" 
                onClick={() => window.history.back()}
                className="flex items-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Back to Dashboard
              </Button>
              <h1 className="text-2xl font-bold text-gray-900">Daily Court List</h1>
            </div>
            <div className="text-right">
              <p className="text-lg font-semibold text-gray-900">
                {formatLegalDate(new Date(selectedDate))}
              </p>
              <p className="text-sm text-gray-600">
                {user?.displayName}
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Controls */}
        <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField>
            <Label htmlFor="date">Select Date</Label>
            <input
              id="date"
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="form-input"
            />
          </FormField>
          
          <FormField>
            <Label htmlFor="courtroom">Courtroom</Label>
            <Select
              id="courtroom"
              value={selectedCourtroom}
              onChange={(e) => setSelectedCourtroom(e.target.value)}
              options={courtrooms}
            />
          </FormField>

          <div className="flex items-end">
            {hasPermission('create_case') && (
              <Button variant="primary" className="w-full">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add New Case
              </Button>
            )}
          </div>
        </div>

        {/* Court Schedule */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Time Slots */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Schedule Overview
            </h3>
            <div className="space-y-2">
              {timeSlots.map(timeSlot => {
                const casesInSlot = getCasesByTimeSlot(timeSlot)
                return (
                  <div
                    key={timeSlot}
                    className="border-2 border-dashed border-gray-300 rounded-lg p-4 min-h-[80px] transition-colors duration-200 hover:border-blue-400"
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, timeSlot)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-semibold text-gray-700">{timeSlot}</span>
                      <span className="text-sm text-gray-500">
                        {casesInSlot.length} case{casesInSlot.length !== 1 ? 's' : ''}
                      </span>
                    </div>
                    {casesInSlot.length === 0 ? (
                      <p className="text-gray-400 text-sm">Drop cases here to schedule</p>
                    ) : (
                      <div className="space-y-1">
                        {casesInSlot.map(case_ => (
                          <div
                            key={case_.id}
                            className={`text-sm p-2 rounded border-l-4 ${getPriorityColor(case_.priority)}`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{case_.caseNumber}</span>
                              <span className={`status-badge ${getCaseStatusColor(case_.status)}`}>
                                {case_.status}
                              </span>
                            </div>
                            <p className="text-gray-600 truncate">{case_.title}</p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          </div>

          {/* Case Details */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Today's Cases
            </h3>
            <div className="space-y-4">
              {cases.map(case_ => (
                <Card
                  key={case_.id}
                  className={`cursor-move border-l-4 ${getPriorityColor(case_.priority)} hover:shadow-lg transition-shadow duration-200`}
                  draggable
                  onDragStart={() => handleDragStart(case_)}
                >
                  <CardContent>
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1">
                        <div className="flex-shrink-0 mt-1">
                          {getTypeIcon(case_.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-lg font-semibold text-gray-900">
                              {case_.caseNumber}
                            </h4>
                            <span className={`status-badge ${getCaseStatusColor(case_.status)}`}>
                              {case_.status}
                            </span>
                          </div>
                          <p className="text-gray-700 mb-2">{case_.title}</p>
                          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                            <div>
                              <span className="font-medium">Time:</span> {case_.scheduledTime}
                            </div>
                            <div>
                              <span className="font-medium">Duration:</span> {case_.estimatedDuration} min
                            </div>
                            <div>
                              <span className="font-medium">Type:</span> {case_.type}
                            </div>
                            <div>
                              <span className="font-medium">Priority:</span> {case_.priority}
                            </div>
                          </div>
                          {case_.advocates.length > 0 && (
                            <div className="mt-2">
                              <span className="text-sm font-medium text-gray-600">Advocates:</span>
                              <p className="text-sm text-gray-600">{case_.advocates.join(', ')}</p>
                            </div>
                          )}
                          {case_.notes && (
                            <div className="mt-2">
                              <span className="text-sm font-medium text-gray-600">Notes:</span>
                              <p className="text-sm text-gray-600">{case_.notes}</p>
                            </div>
                          )}
                        </div>
                      </div>
                      {hasPermission('edit_case') && (
                        <div className="flex-shrink-0 ml-4 space-y-2">
                          <Button variant="primary" size="small">
                            Open
                          </Button>
                          {case_.status === 'PENDING' && (
                            <Button 
                              variant="success" 
                              size="small"
                              onClick={() => updateCaseStatus(case_.id, 'ACTIVE')}
                            >
                              Start
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Summary */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
          <InfoCard type="info" title="Total Cases">
            <p className="text-2xl font-bold">{cases.length}</p>
            <p className="text-sm">Scheduled for today</p>
          </InfoCard>
          
          <InfoCard type="warning" title="High Priority">
            <p className="text-2xl font-bold">{cases.filter(c => c.priority === 'HIGH').length}</p>
            <p className="text-sm">Require immediate attention</p>
          </InfoCard>
          
          <InfoCard type="success" title="Estimated Time">
            <p className="text-2xl font-bold">
              {Math.round(cases.reduce((total, case_) => total + case_.estimatedDuration, 0) / 60 * 10) / 10}h
            </p>
            <p className="text-sm">Total court time needed</p>
          </InfoCard>
        </div>
      </main>
    </div>
  )
}

// Export with authentication protection
export default withAuth(CourtListPage, ['manage_court_list'])
