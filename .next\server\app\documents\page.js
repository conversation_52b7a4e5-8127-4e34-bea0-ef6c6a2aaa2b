/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/documents/page";
exports.ids = ["app/documents/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocuments%2Fpage&page=%2Fdocuments%2Fpage&appPaths=%2Fdocuments%2Fpage&pagePath=private-next-app-dir%2Fdocuments%2Fpage.tsx&appDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocuments%2Fpage&page=%2Fdocuments%2Fpage&appPaths=%2Fdocuments%2Fpage&pagePath=private-next-app-dir%2Fdocuments%2Fpage.tsx&appDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/documents/page.tsx */ \"(rsc)/./src/app/documents/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'documents',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/documents/page\",\n        pathname: \"/documents\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZkb2N1bWVudHMlMkZwYWdlJnBhZ2U9JTJGZG9jdW1lbnRzJTJGcGFnZSZhcHBQYXRocz0lMkZkb2N1bWVudHMlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGZG9jdW1lbnRzJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q3NhcmNoJTVDRGVza3RvcCU1Q0tlc2lUcmFjayU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDc2FyY2glNUNEZXNrdG9wJTVDS2VzaVRyYWNrJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxzQkFBc0Isb0pBQStGO0FBQ3JILHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsZ09BQW1GO0FBQ3pHLG9CQUFvQixvS0FBd0c7QUFHMUg7QUFHQTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0Msc2ZBQXVQO0FBQzNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBdVA7QUFDM1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFHckI7QUFDRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFHRTtBQUNGO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBtb2R1bGUwID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxzYXJjaFxcXFxEZXNrdG9wXFxcXEtlc2lUcmFja1xcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIik7XG5jb25zdCBtb2R1bGUxID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMiA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTMgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIik7XG5jb25zdCBwYWdlNCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcc2FyY2hcXFxcRGVza3RvcFxcXFxLZXNpVHJhY2tcXFxcc3JjXFxcXGFwcFxcXFxkb2N1bWVudHNcXFxccGFnZS50c3hcIik7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNzcidcbn07XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ2RvY3VtZW50cycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFtwYWdlNCwgXCJDOlxcXFxVc2Vyc1xcXFxzYXJjaFxcXFxEZXNrdG9wXFxcXEtlc2lUcmFja1xcXFxzcmNcXFxcYXBwXFxcXGRvY3VtZW50c1xcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFDOlxcXFxVc2Vyc1xcXFxzYXJjaFxcXFxEZXNrdG9wXFxcXEtlc2lUcmFja1xcXFxzcmNcXFxcYXBwXFxcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogW21vZHVsZTAsIFwiQzpcXFxcVXNlcnNcXFxcc2FyY2hcXFxcRGVza3RvcFxcXFxLZXNpVHJhY2tcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFttb2R1bGUxLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4nZm9yYmlkZGVuJzogW21vZHVsZTIsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiXSxcbid1bmF1dGhvcml6ZWQnOiBbbW9kdWxlMywgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCJdLFxuICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhQzpcXFxcVXNlcnNcXFxcc2FyY2hcXFxcRGVza3RvcFxcXFxLZXNpVHJhY2tcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkM6XFxcXFVzZXJzXFxcXHNhcmNoXFxcXERlc2t0b3BcXFxcS2VzaVRyYWNrXFxcXHNyY1xcXFxhcHBcXFxcZG9jdW1lbnRzXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9kb2N1bWVudHMvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvZG9jdW1lbnRzXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogJycsXG4gICAgICAgIGZpbGVuYW1lOiAnJyxcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocuments%2Fpage&page=%2Fdocuments%2Fpage&appPaths=%2Fdocuments%2Fpage&pagePath=private-next-app-dir%2Fdocuments%2Fpage.tsx&appDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC1pbnRlciU1QyUyMiU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBMkkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHNhcmNoXFxcXERlc2t0b3BcXFxcS2VzaVRyYWNrXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxBdXRoQ29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cdocuments%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cdocuments%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/documents/page.tsx */ \"(rsc)/./src/app/documents/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2RvY3VtZW50cyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBd0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHNhcmNoXFxcXERlc2t0b3BcXFxcS2VzaVRyYWNrXFxcXHNyY1xcXFxhcHBcXFxcZG9jdW1lbnRzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cdocuments%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FyY2hcXERlc2t0b3BcXEtlc2lUcmFja1xcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/documents/page.tsx":
/*!************************************!*\
  !*** ./src/app/documents/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\KesiTrack\\src\\app\\documents\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8fb898129f22\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhcmNoXFxEZXNrdG9wXFxLZXNpVHJhY2tcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhmYjg5ODEyOWYyMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"KesiTrack - Legal Case Management System\",\n    description: \"AI-powered legal case management system for the Republic of Kenya Judiciary\",\n    keywords: \"legal, case management, Kenya, judiciary, court, magistrate\",\n    authors: [\n        {\n            name: \"Republic of Kenya - Judiciary\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} h-full bg-gray-50 font-sans antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth),
/* harmony export */   withAuth: () => (/* binding */ withAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\KesiTrack\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\KesiTrack\\src\\contexts\\AuthContext.tsx",
"useAuth",
);const withAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call withAuth() from the server but withAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\KesiTrack\\src\\contexts\\AuthContext.tsx",
"withAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC1pbnRlciU1QyUyMiU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBMkkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHNhcmNoXFxcXERlc2t0b3BcXFxcS2VzaVRyYWNrXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxBdXRoQ29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cdocuments%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cdocuments%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/documents/page.tsx */ \"(ssr)/./src/app/documents/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NhcmNoJTVDJTVDRGVza3RvcCU1QyU1Q0tlc2lUcmFjayU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2RvY3VtZW50cyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBd0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHNhcmNoXFxcXERlc2t0b3BcXFxcS2VzaVRyYWNrXFxcXHNyY1xcXFxhcHBcXFxcZG9jdW1lbnRzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csarch%5C%5CDesktop%5C%5CKesiTrack%5C%5Csrc%5C%5Capp%5C%5Cdocuments%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/documents/page.tsx":
/*!************************************!*\
  !*** ./src/app/documents/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Form */ \"(ssr)/./src/components/ui/Form.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction DocumentsPage() {\n    const { user, hasPermission } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            title: 'Judgment - CR 45/2024 MC',\n            templateId: 'criminal-judgment',\n            content: 'Draft judgment for Republic vs John Doe...',\n            status: 'draft',\n            createdAt: '2024-07-10T09:00:00Z',\n            updatedAt: '2024-07-10T10:30:00Z',\n            caseNumber: 'CR 45/2024 MC'\n        },\n        {\n            id: '2',\n            title: 'Bail Order - CR 67/2024 MC',\n            templateId: 'bail-order',\n            content: 'Bail order for Republic vs Mary Johnson...',\n            status: 'approved',\n            createdAt: '2024-07-09T14:00:00Z',\n            updatedAt: '2024-07-09T15:00:00Z',\n            caseNumber: 'CR 67/2024 MC'\n        }\n    ]);\n    const [showTemplateSelector, setShowTemplateSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingDocument, setEditingDocument] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Legal document templates\n    const templates = [\n        {\n            id: 'criminal-judgment',\n            name: 'Criminal Case Judgment',\n            description: 'Standard template for criminal case judgments',\n            category: 'judgment',\n            content: `REPUBLIC OF KENYA\nIN THE MAGISTRATE'S COURT AT [COURT_LOCATION]\n\nCRIMINAL CASE NO. [CASE_NUMBER]\n\nREPUBLIC\nVS\n[ACCUSED_NAME]\n\nJUDGMENT\n\nBefore: [MAGISTRATE_NAME], Senior Resident Magistrate\n\nDate: [JUDGMENT_DATE]\n\nCHARGES:\n[CHARGES_DETAILS]\n\nFACTS:\n[CASE_FACTS]\n\nANALYSIS:\n[LEGAL_ANALYSIS]\n\nFINDING:\n[COURT_FINDING]\n\nSENTENCE:\n[SENTENCE_DETAILS]\n\nDelivered in open court this [JUDGMENT_DATE]\n\n[MAGISTRATE_NAME]\nSenior Resident Magistrate`,\n            fields: [\n                {\n                    name: 'COURT_LOCATION',\n                    label: 'Court Location',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'CASE_NUMBER',\n                    label: 'Case Number',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'ACCUSED_NAME',\n                    label: 'Accused Name',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'MAGISTRATE_NAME',\n                    label: 'Magistrate Name',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'JUDGMENT_DATE',\n                    label: 'Judgment Date',\n                    type: 'date',\n                    required: true\n                },\n                {\n                    name: 'CHARGES_DETAILS',\n                    label: 'Charges Details',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'CASE_FACTS',\n                    label: 'Case Facts',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'LEGAL_ANALYSIS',\n                    label: 'Legal Analysis',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'COURT_FINDING',\n                    label: 'Court Finding',\n                    type: 'select',\n                    options: [\n                        'Guilty',\n                        'Not Guilty'\n                    ],\n                    required: true\n                },\n                {\n                    name: 'SENTENCE_DETAILS',\n                    label: 'Sentence Details',\n                    type: 'text',\n                    required: false\n                }\n            ]\n        },\n        {\n            id: 'bail-order',\n            name: 'Bail Order',\n            description: 'Template for bail application orders',\n            category: 'order',\n            content: `REPUBLIC OF KENYA\nIN THE MAGISTRATE'S COURT AT [COURT_LOCATION]\n\nCRIMINAL CASE NO. [CASE_NUMBER]\n\nREPUBLIC\nVS\n[ACCUSED_NAME]\n\nBAIL ORDER\n\nBefore: [MAGISTRATE_NAME], Senior Resident Magistrate\n\nDate: [ORDER_DATE]\n\nUPON hearing the application for bail by the accused person [ACCUSED_NAME] charged with [CHARGES];\n\nAND UPON considering the submissions by the prosecution and defense;\n\nIT IS HEREBY ORDERED THAT:\n\n1. The accused person [ACCUSED_NAME] is hereby admitted to bail in the sum of KSh [BAIL_AMOUNT];\n\n2. The accused shall deposit the said sum with the court or provide [SURETY_TYPE];\n\n3. The accused shall report to [REPORTING_STATION] every [REPORTING_FREQUENCY];\n\n4. The accused shall not interfere with witnesses;\n\n5. The accused shall surrender all travel documents to the court;\n\n6. [ADDITIONAL_CONDITIONS]\n\n7. The matter is mentioned on [MENTION_DATE] for [MENTION_PURPOSE].\n\nDated this [ORDER_DATE]\n\n[MAGISTRATE_NAME]\nSenior Resident Magistrate`,\n            fields: [\n                {\n                    name: 'COURT_LOCATION',\n                    label: 'Court Location',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'CASE_NUMBER',\n                    label: 'Case Number',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'ACCUSED_NAME',\n                    label: 'Accused Name',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'MAGISTRATE_NAME',\n                    label: 'Magistrate Name',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'ORDER_DATE',\n                    label: 'Order Date',\n                    type: 'date',\n                    required: true\n                },\n                {\n                    name: 'CHARGES',\n                    label: 'Charges',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'BAIL_AMOUNT',\n                    label: 'Bail Amount (KSh)',\n                    type: 'number',\n                    required: true\n                },\n                {\n                    name: 'SURETY_TYPE',\n                    label: 'Surety Type',\n                    type: 'select',\n                    options: [\n                        'Cash Bail',\n                        'Bond with Surety',\n                        'Personal Bond'\n                    ],\n                    required: true\n                },\n                {\n                    name: 'REPORTING_STATION',\n                    label: 'Reporting Station',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'REPORTING_FREQUENCY',\n                    label: 'Reporting Frequency',\n                    type: 'select',\n                    options: [\n                        'Monday',\n                        'Tuesday',\n                        'Wednesday',\n                        'Thursday',\n                        'Friday',\n                        'Weekly',\n                        'Bi-weekly'\n                    ],\n                    required: true\n                },\n                {\n                    name: 'ADDITIONAL_CONDITIONS',\n                    label: 'Additional Conditions',\n                    type: 'text',\n                    required: false\n                },\n                {\n                    name: 'MENTION_DATE',\n                    label: 'Next Mention Date',\n                    type: 'date',\n                    required: true\n                },\n                {\n                    name: 'MENTION_PURPOSE',\n                    label: 'Mention Purpose',\n                    type: 'text',\n                    required: true\n                }\n            ]\n        },\n        {\n            id: 'civil-judgment',\n            name: 'Civil Case Judgment',\n            description: 'Standard template for civil case judgments',\n            category: 'judgment',\n            content: `REPUBLIC OF KENYA\nIN THE MAGISTRATE'S COURT AT [COURT_LOCATION]\n\nCIVIL CASE NO. [CASE_NUMBER]\n\n[PLAINTIFF_NAME]\nVS\n[DEFENDANT_NAME]\n\nJUDGMENT\n\nBefore: [MAGISTRATE_NAME], Senior Resident Magistrate\n\nDate: [JUDGMENT_DATE]\n\nPARTIES:\nPlaintiff: [PLAINTIFF_NAME]\nDefendant: [DEFENDANT_NAME]\n\nCLAIM:\n[CLAIM_DETAILS]\n\nDEFENSE:\n[DEFENSE_DETAILS]\n\nISSUES FOR DETERMINATION:\n[ISSUES]\n\nANALYSIS AND FINDINGS:\n[ANALYSIS]\n\nORDERS:\n[COURT_ORDERS]\n\nCOSTS:\n[COSTS_ORDER]\n\nDelivered in open court this [JUDGMENT_DATE]\n\n[MAGISTRATE_NAME]\nSenior Resident Magistrate`,\n            fields: [\n                {\n                    name: 'COURT_LOCATION',\n                    label: 'Court Location',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'CASE_NUMBER',\n                    label: 'Case Number',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'PLAINTIFF_NAME',\n                    label: 'Plaintiff Name',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'DEFENDANT_NAME',\n                    label: 'Defendant Name',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'MAGISTRATE_NAME',\n                    label: 'Magistrate Name',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'JUDGMENT_DATE',\n                    label: 'Judgment Date',\n                    type: 'date',\n                    required: true\n                },\n                {\n                    name: 'CLAIM_DETAILS',\n                    label: 'Claim Details',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'DEFENSE_DETAILS',\n                    label: 'Defense Details',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'ISSUES',\n                    label: 'Issues for Determination',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'ANALYSIS',\n                    label: 'Analysis and Findings',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'COURT_ORDERS',\n                    label: 'Court Orders',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'COSTS_ORDER',\n                    label: 'Costs Order',\n                    type: 'text',\n                    required: true\n                }\n            ]\n        }\n    ];\n    const createNewDocument = (template)=>{\n        const newDoc = {\n            id: Date.now().toString(),\n            title: `New ${template.name}`,\n            templateId: template.id,\n            content: template.content,\n            status: 'draft',\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setDocuments((prev)=>[\n                newDoc,\n                ...prev\n            ]);\n        setEditingDocument(newDoc);\n        setShowTemplateSelector(false);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'draft':\n                return 'status-pending';\n            case 'review':\n                return 'status-active';\n            case 'approved':\n                return 'status-completed';\n            case 'published':\n                return 'status-completed';\n            default:\n                return 'status-pending';\n        }\n    };\n    const getCategoryIcon = (category)=>{\n        switch(category){\n            case 'judgment':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-8 h-8 text-blue-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 11\n                }, this);\n            case 'order':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-8 h-8 text-green-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this);\n            case 'notice':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-8 h-8 text-yellow-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2zM4 7h8V5H4v2z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 11\n                }, this);\n            case 'bail':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-8 h-8 text-purple-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this);\n            case 'sentence':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-8 h-8 text-red-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"secondary\",\n                                        onClick: ()=>window.history.back(),\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 19l-7-7 7-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Document Management\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    hasPermission('draft_documents') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"primary\",\n                                        onClick: ()=>setShowTemplateSelector(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"New Document\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: user?.displayName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                children: showTemplateSelector ? /* Template Selection */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Choose a Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"secondary\",\n                                    onClick: ()=>setShowTemplateSelector(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: templates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.QuickActionCard, {\n                                    title: template.name,\n                                    description: template.description,\n                                    icon: getCategoryIcon(template.category),\n                                    onClick: ()=>createNewDocument(template)\n                                }, template.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.InfoCard, {\n                                type: \"info\",\n                                title: \"Template Guidelines\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Choose the appropriate template for your document type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"All required fields must be filled before the document can be finalized\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Templates follow standard Kenyan legal formatting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"You can customize the content after selecting a template\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 11\n                }, this) : editingDocument ? /* Document Editor */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DocumentEditor, {\n                    document: editingDocument,\n                    template: templates.find((t)=>t.id === editingDocument.templateId),\n                    onSave: (updatedDoc)=>{\n                        setDocuments((prev)=>prev.map((doc)=>doc.id === updatedDoc.id ? updatedDoc : doc));\n                        setEditingDocument(null);\n                    },\n                    onCancel: ()=>setEditingDocument(null)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 11\n                }, this) : /* Document List */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                    children: \"My Documents\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 gap-4\",\n                                    children: documents.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"hover:shadow-lg transition-shadow duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                                    children: doc.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: \"Status:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                                    lineNumber: 407,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: `ml-2 status-badge ${getStatusColor(doc.status)}`,\n                                                                                    children: doc.status.toUpperCase()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                                    lineNumber: 408,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                            lineNumber: 406,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: \"Created:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                                    lineNumber: 413,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-2\",\n                                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatLegalDate)(new Date(doc.createdAt))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                                    lineNumber: 414,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                            lineNumber: 412,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: \"Updated:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                                    lineNumber: 417,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-2\",\n                                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatLegalDate)(new Date(doc.updatedAt))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                                    lineNumber: 418,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        doc.caseNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: \"Case:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                                    lineNumber: 422,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-2\",\n                                                                                    children: doc.caseNumber\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                                    lineNumber: 423,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 ml-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"primary\",\n                                                                    size: \"small\",\n                                                                    onClick: ()=>setEditingDocument(doc),\n                                                                    children: \"Edit\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"secondary\",\n                                                                    size: \"small\",\n                                                                    onClick: ()=>alert('Preview feature coming soon!'),\n                                                                    children: \"Preview\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                hasPermission('approve_documents') && doc.status === 'review' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"success\",\n                                                                    size: \"small\",\n                                                                    onClick: ()=>{\n                                                                        setDocuments((prev)=>prev.map((d)=>d.id === doc.id ? {\n                                                                                    ...d,\n                                                                                    status: 'approved'\n                                                                                } : d));\n                                                                    },\n                                                                    children: \"Approve\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, doc.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.InfoCard, {\n                                    type: \"info\",\n                                    title: \"Total Documents\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: documents.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.InfoCard, {\n                                    type: \"warning\",\n                                    title: \"Drafts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: documents.filter((d)=>d.status === 'draft').length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.InfoCard, {\n                                    type: \"info\",\n                                    title: \"Under Review\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: documents.filter((d)=>d.status === 'review').length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.InfoCard, {\n                                    type: \"success\",\n                                    title: \"Approved\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: documents.filter((d)=>d.status === 'approved').length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, this);\n}\n// Simple Document Editor Component\nfunction DocumentEditor({ document, template, onSave, onCancel }) {\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(document.content);\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(document.title);\n    const [fieldValues, setFieldValues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleSave = ()=>{\n        let updatedContent = content;\n        // Replace template fields with values\n        if (template) {\n            template.fields.forEach((field)=>{\n                const value = fieldValues[field.name] || `[${field.name}]`;\n                updatedContent = updatedContent.replace(new RegExp(`\\\\[${field.name}\\\\]`, 'g'), value);\n            });\n        }\n        const updatedDoc = {\n            ...document,\n            title,\n            content: updatedContent,\n            updatedAt: new Date().toISOString()\n        };\n        onSave(updatedDoc);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"Edit Document\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                        lineNumber: 525,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"secondary\",\n                                onClick: onCancel,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"primary\",\n                                onClick: handleSave,\n                                children: \"Save Document\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                lineNumber: 524,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    template && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Document Fields\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"title\",\n                                                        children: \"Document Title\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"title\",\n                                                        value: title,\n                                                        onChange: (e)=>setTitle(e.target.value),\n                                                        placeholder: \"Enter document title\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 19\n                                            }, this),\n                                            template.fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: field.name,\n                                                            required: field.required,\n                                                            children: field.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        field.type === 'select' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                            id: field.name,\n                                                            value: fieldValues[field.name] || '',\n                                                            onChange: (e)=>setFieldValues((prev)=>({\n                                                                        ...prev,\n                                                                        [field.name]: e.target.value\n                                                                    })),\n                                                            options: field.options?.map((opt)=>({\n                                                                    value: opt,\n                                                                    label: opt\n                                                                })) || [],\n                                                            placeholder: `Select ${field.label.toLowerCase()}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Form__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: field.name,\n                                                            type: field.type,\n                                                            value: fieldValues[field.name] || '',\n                                                            onChange: (e)=>setFieldValues((prev)=>({\n                                                                        ...prev,\n                                                                        [field.name]: e.target.value\n                                                                    })),\n                                                            placeholder: `Enter ${field.label.toLowerCase()}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, field.name, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 21\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: template ? 'lg:col-span-2' : 'lg:col-span-3',\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"h-[600px] flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Document Content\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: content,\n                                        onChange: (e)=>setContent(e.target.value),\n                                        className: \"w-full h-full p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"Enter document content...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n                lineNumber: 536,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\app\\\\documents\\\\page.tsx\",\n        lineNumber: 523,\n        columnNumber: 5\n    }, this);\n}\n// Export with authentication protection\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.withAuth)(DocumentsPage, [\n    'draft_documents'\n]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/documents/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   ButtonGroup: () => (/* binding */ ButtonGroup),\n/* harmony export */   IconButton: () => (/* binding */ IconButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n/**\n * Large, accessible button component designed for non-technical users\n * Features:\n * - Large touch targets (minimum 44px)\n * - Clear visual feedback\n * - Loading states\n * - High contrast colors\n * - Keyboard navigation support\n */ function Button({ variant = 'primary', size = 'medium', loading = false, icon, children, className, disabled, ...props }) {\n    const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed';\n    const variantClasses = {\n        primary: 'btn-primary focus:ring-blue-300',\n        secondary: 'btn-secondary focus:ring-gray-300',\n        success: 'btn-success focus:ring-green-300',\n        warning: 'btn-warning focus:ring-yellow-300',\n        danger: 'btn-danger focus:ring-red-300'\n    };\n    const sizeClasses = {\n        small: 'px-4 py-2 text-base min-h-[40px]',\n        medium: 'px-6 py-3 text-lg min-h-[48px]',\n        large: 'px-8 py-4 text-xl min-h-[56px]'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variantClasses[variant], sizeClasses[size], className),\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-3 h-5 w-5\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            icon && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 80,\n                columnNumber: 28\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Icon-only button for actions like edit, delete, etc.\n */ function IconButton({ variant = 'secondary', size = 'medium', loading = false, icon, className, disabled, 'aria-label': ariaLabel, ...props }) {\n    const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed';\n    const variantClasses = {\n        primary: 'btn-primary focus:ring-blue-300',\n        secondary: 'btn-secondary focus:ring-gray-300',\n        success: 'btn-success focus:ring-green-300',\n        warning: 'btn-warning focus:ring-yellow-300',\n        danger: 'btn-danger focus:ring-red-300'\n    };\n    const sizeClasses = {\n        small: 'p-2 min-h-[40px] min-w-[40px]',\n        medium: 'p-3 min-h-[48px] min-w-[48px]',\n        large: 'p-4 min-h-[56px] min-w-[56px]'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variantClasses[variant], sizeClasses[size], className),\n        disabled: disabled || loading,\n        \"aria-label\": ariaLabel,\n        ...props,\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"animate-spin h-5 w-5\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    className: \"opacity-25\",\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"10\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    className: \"opacity-75\",\n                    fill: \"currentColor\",\n                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n            lineNumber: 128,\n            columnNumber: 9\n        }, this) : icon\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Button group for related actions\n */ function ButtonGroup({ children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex flex-wrap gap-3', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   InfoCard: () => (/* binding */ InfoCard),\n/* harmony export */   QuickActionCard: () => (/* binding */ QuickActionCard),\n/* harmony export */   StatusCard: () => (/* binding */ StatusCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n/**\n * Card component for organizing content in a clean, accessible way\n * Designed for easy scanning by judicial officers\n */ function Card({ children, className, padding = 'medium' }) {\n    const paddingClasses = {\n        none: 'p-0',\n        small: 'p-4',\n        medium: 'p-6',\n        large: 'p-8'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('card', paddingClasses[padding], className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Card header with optional actions\n */ function CardHeader({ children, className, actions }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('card-header', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                actions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 ml-4\",\n                    children: actions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Card content area\n */ function CardContent({ children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('space-y-4', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Card footer for actions or additional info\n */ function CardFooter({ children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('pt-4 mt-6 border-t border-gray-200', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\nfunction StatusCard({ title, status, description, statusColor = 'status-pending', icon, actions, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('hover:shadow-lg transition-shadow duration-200', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3\",\n                        children: [\n                            icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0 mt-1\",\n                                children: icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('status-badge', statusColor),\n                                            children: status\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-base\",\n                                        children: description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this),\n                    actions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 ml-4\",\n                        children: actions\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\nfunction QuickActionCard({ title, description, icon, onClick, className, disabled = false }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        disabled: disabled,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('card text-left w-full hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed', 'hover:scale-105 active:scale-95', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 p-3 bg-blue-50 rounded-lg\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-base\",\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6 text-gray-400\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 5l7 7-7 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\nfunction InfoCard({ type = 'info', title, children, className }) {\n    const typeClasses = {\n        info: 'bg-blue-50 border-blue-200 text-blue-800',\n        warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n        success: 'bg-green-50 border-green-200 text-green-800',\n        error: 'bg-red-50 border-red-200 text-red-800'\n    };\n    const iconClasses = {\n        info: 'text-blue-500',\n        warning: 'text-yellow-500',\n        success: 'text-green-500',\n        error: 'text-red-500'\n    };\n    const icons = {\n        info: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 235,\n            columnNumber: 7\n        }, this),\n        warning: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, this),\n        success: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 246,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, this),\n        error: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 251,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 250,\n            columnNumber: 7\n        }, this)\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('rounded-lg border-2 p-4', typeClasses[type], className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex-shrink-0', iconClasses[type]),\n                    children: icons[type]\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-base\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 258,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 257,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Form.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Form.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Checkbox: () => (/* binding */ Checkbox),\n/* harmony export */   FormField: () => (/* binding */ FormField),\n/* harmony export */   FormSection: () => (/* binding */ FormSection),\n/* harmony export */   Input: () => (/* binding */ Input),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup),\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n/**\n * Form field wrapper for consistent spacing and layout\n */ function FormField({ children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('space-y-2', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Form label with required indicator\n */ function Label({ required, children, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('form-label', className),\n        ...props,\n        children: [\n            children,\n            required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-red-500 ml-1\",\n                children: \"*\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 49,\n                columnNumber: 20\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Text input with error handling and help text\n */ function Input({ error, helpText, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('form-input', error && 'border-red-500 focus:border-red-500 focus:ring-red-500', className),\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-red-600 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 mr-1 flex-shrink-0\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this),\n            helpText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-gray-600\",\n                children: helpText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Textarea with error handling and help text\n */ function Textarea({ error, helpText, className, rows = 4, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                rows: rows,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('form-input resize-vertical min-h-[100px]', error && 'border-red-500 focus:border-red-500 focus:ring-red-500', className),\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-red-600 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 mr-1 flex-shrink-0\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this),\n            helpText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-gray-600\",\n                children: helpText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Select dropdown with error handling and help text\n */ function Select({ error, helpText, options, placeholder, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('form-input', error && 'border-red-500 focus:border-red-500 focus:ring-red-500', className),\n                ...props,\n                children: [\n                    placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                        value: \"\",\n                        disabled: true,\n                        children: placeholder\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this),\n                    options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                            value: option.value,\n                            disabled: option.disabled,\n                            children: option.label\n                        }, option.value, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-red-600 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 mr-1 flex-shrink-0\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this),\n            helpText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-gray-600\",\n                children: helpText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\nfunction Checkbox({ label, error, helpText, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"checkbox\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('mt-1 h-5 w-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 focus:ring-2', error && 'border-red-500', className),\n                        ...props\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-base font-medium text-gray-700 cursor-pointer\",\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            helpText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-600\",\n                                children: helpText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-red-600 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 mr-1 flex-shrink-0\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\nfunction RadioGroup({ name, options, value, onChange, error, helpText, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"radio\",\n                                id: `${name}-${option.value}`,\n                                name: name,\n                                value: option.value,\n                                checked: value === option.value,\n                                onChange: (e)=>onChange(e.target.value),\n                                disabled: option.disabled,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('mt-1 h-5 w-5 text-blue-600 border-2 border-gray-300 focus:ring-blue-500 focus:ring-2', error && 'border-red-500')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: `${name}-${option.value}`,\n                                        className: \"text-base font-medium text-gray-700 cursor-pointer\",\n                                        children: option.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    option.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-gray-600\",\n                                        children: option.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, option.value, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-red-600 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 mr-1 flex-shrink-0\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 272,\n                columnNumber: 9\n            }, this),\n            helpText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-gray-600\",\n                children: helpText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\nfunction FormSection({ title, description, children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('space-y-6', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-base text-gray-600\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\components\\\\ui\\\\Form.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Form.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Role-based permissions\nconst PERMISSIONS = {\n    magistrate: [\n        'view_all_cases',\n        'create_case',\n        'edit_case',\n        'delete_case',\n        'start_recording',\n        'draft_judgment',\n        'finalize_judgment',\n        'manage_court_list',\n        'view_analytics',\n        'manage_templates',\n        'approve_documents'\n    ],\n    clerk: [\n        'view_assigned_cases',\n        'create_case',\n        'edit_case',\n        'manage_court_list',\n        'draft_documents',\n        'prepare_templates',\n        'schedule_hearings'\n    ],\n    advocate: [\n        'view_own_cases',\n        'view_case_status',\n        'view_documents',\n        'view_schedules'\n    ]\n};\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check for existing session on mount\n            const checkExistingSession = {\n                \"AuthProvider.useEffect.checkExistingSession\": ()=>{\n                    try {\n                        const storedUser = localStorage.getItem('kesitrack_user');\n                        if (storedUser) {\n                            const userData = JSON.parse(storedUser);\n                            // Check if session is still valid (24 hours)\n                            const loginTime = new Date(userData.loginTime);\n                            const now = new Date();\n                            const hoursDiff = (now.getTime() - loginTime.getTime()) / (1000 * 60 * 60);\n                            if (hoursDiff < 24) {\n                                setUser(userData);\n                            } else {\n                                // Session expired\n                                localStorage.removeItem('kesitrack_user');\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error checking session:', error);\n                        localStorage.removeItem('kesitrack_user');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkExistingSession\"];\n            checkExistingSession();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (username, password, role)=>{\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // For demo purposes, accept any credentials\n            // In real implementation, this would validate against backend\n            const userData = {\n                username,\n                role: role,\n                loginTime: new Date().toISOString(),\n                displayName: getDisplayName(username, role)\n            };\n            localStorage.setItem('kesitrack_user', JSON.stringify(userData));\n            setUser(userData);\n            return true;\n        } catch (error) {\n            console.error('Login error:', error);\n            return false;\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem('kesitrack_user');\n        setUser(null);\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        if (!user) return false;\n        return PERMISSIONS[user.role]?.includes(permission) || false;\n    };\n    const getDisplayName = (username, role)=>{\n        const roleNames = {\n            magistrate: 'Magistrate',\n            clerk: 'Court Clerk',\n            advocate: 'Advocate'\n        };\n        // In real implementation, this would come from user database\n        return `${roleNames[role]} ${username}`;\n    };\n    const value = {\n        user,\n        login,\n        logout,\n        isLoading,\n        hasPermission\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n// Higher-order component for protecting routes\nfunction withAuth(Component, requiredPermissions) {\n    return function AuthenticatedComponent(props) {\n        const { user, isLoading, hasPermission } = useAuth();\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-10 h-10 text-white animate-spin\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"Loading KesiTrack...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Please wait while we verify your session\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            window.location.href = '/login';\n            return null;\n        }\n        // Check permissions if required\n        if (requiredPermissions && requiredPermissions.length > 0) {\n            const hasRequiredPermissions = requiredPermissions.every((permission)=>hasPermission(permission));\n            if (!hasRequiredPermissions) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-10 h-10 text-red-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                children: \"Access Denied\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"You do not have permission to access this feature. Please contact your administrator if you believe this is an error.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.history.back(),\n                                className: \"btn-secondary\",\n                                children: \"Go Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KesiTrack\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 222,\n            columnNumber: 12\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateBailAmount: () => (/* binding */ calculateBailAmount),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCourtTime: () => (/* binding */ formatCourtTime),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatKSh: () => (/* binding */ formatKSh),\n/* harmony export */   formatLegalDate: () => (/* binding */ formatLegalDate),\n/* harmony export */   generateCaseNumber: () => (/* binding */ generateCaseNumber),\n/* harmony export */   getCaseStatusColor: () => (/* binding */ getCaseStatusColor),\n/* harmony export */   getUserFriendlyError: () => (/* binding */ getUserFriendlyError),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   scrollToElement: () => (/* binding */ scrollToElement),\n/* harmony export */   simpleSearch: () => (/* binding */ simpleSearch),\n/* harmony export */   validateKenyanID: () => (/* binding */ validateKenyanID),\n/* harmony export */   validateKenyanPhone: () => (/* binding */ validateKenyanPhone)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Utility function to merge Tailwind CSS classes\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format currency in Kenya Shillings\n */ function formatKSh(amount) {\n    return new Intl.NumberFormat('en-KE', {\n        style: 'currency',\n        currency: 'KES',\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).format(amount);\n}\n/**\n * Format date for Kenyan legal system\n */ function formatLegalDate(date) {\n    return new Intl.DateTimeFormat('en-KE', {\n        day: 'numeric',\n        month: 'long',\n        year: 'numeric'\n    }).format(date);\n}\n/**\n * Format time for court proceedings\n */ function formatCourtTime(date) {\n    return new Intl.DateTimeFormat('en-KE', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n    }).format(date);\n}\n/**\n * Generate case number format\n */ function generateCaseNumber(type, year, sequence, court = 'MC') {\n    const typeCode = {\n        CRIMINAL: 'CR',\n        CIVIL: 'CV',\n        FAMILY: 'FM',\n        COMMERCIAL: 'CM'\n    }[type];\n    return `${typeCode} ${sequence}/${year} ${court}`;\n}\n/**\n * Validate Kenya ID number\n */ function validateKenyanID(id) {\n    const idRegex = /^\\d{8}$/;\n    return idRegex.test(id);\n}\n/**\n * Validate Kenya phone number\n */ function validateKenyanPhone(phone) {\n    const phoneRegex = /^(\\+254|0)[17]\\d{8}$/;\n    return phoneRegex.test(phone);\n}\n/**\n * Get case status color for UI\n */ function getCaseStatusColor(status) {\n    const statusColors = {\n        'PENDING': 'status-pending',\n        'ACTIVE': 'status-active',\n        'HEARING': 'status-active',\n        'JUDGMENT': 'status-active',\n        'COMPLETED': 'status-completed',\n        'DISMISSED': 'status-cancelled',\n        'WITHDRAWN': 'status-cancelled'\n    };\n    return statusColors[status] || 'status-pending';\n}\n/**\n * Calculate bail amount based on offense type (simplified)\n */ function calculateBailAmount(offenseType, offenseGravity) {\n    const baseAmounts = {\n        'THEFT': {\n            LOW: 10000,\n            MEDIUM: 25000,\n            HIGH: 50000\n        },\n        'ASSAULT': {\n            LOW: 15000,\n            MEDIUM: 30000,\n            HIGH: 75000\n        },\n        'FRAUD': {\n            LOW: 50000,\n            MEDIUM: 100000,\n            HIGH: 200000\n        },\n        'DRUG_OFFENSE': {\n            LOW: 20000,\n            MEDIUM: 50000,\n            HIGH: 100000\n        },\n        'TRAFFIC': {\n            LOW: 5000,\n            MEDIUM: 15000,\n            HIGH: 30000\n        },\n        'DEFAULT': {\n            LOW: 10000,\n            MEDIUM: 25000,\n            HIGH: 50000\n        }\n    };\n    const amounts = baseAmounts[offenseType] || baseAmounts.DEFAULT;\n    return amounts[offenseGravity];\n}\n/**\n * Get user-friendly error messages\n */ function getUserFriendlyError(error) {\n    const errorMessages = {\n        'NETWORK_ERROR': 'Please check your internet connection and try again.',\n        'VALIDATION_ERROR': 'Please check the information you entered and try again.',\n        'PERMISSION_ERROR': 'You do not have permission to perform this action.',\n        'NOT_FOUND': 'The requested information could not be found.',\n        'SERVER_ERROR': 'There was a problem with the system. Please try again later.',\n        'TIMEOUT': 'The request took too long. Please try again.'\n    };\n    return errorMessages[error] || 'An unexpected error occurred. Please try again.';\n}\n/**\n * Debounce function for search inputs\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Simple text search function\n */ function simpleSearch(items, searchTerm, searchFields) {\n    if (!searchTerm.trim()) return items;\n    const term = searchTerm.toLowerCase();\n    return items.filter((item)=>searchFields.some((field)=>{\n            const value = getNestedValue(item, field);\n            return value && value.toString().toLowerCase().includes(term);\n        }));\n}\n/**\n * Get nested object value by path\n */ function getNestedValue(obj, path) {\n    return path.split('.').reduce((current, key)=>current?.[key], obj);\n}\n/**\n * Format file size for display\n */ function formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = [\n        'Bytes',\n        'KB',\n        'MB',\n        'GB'\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n/**\n * Check if user is on mobile device\n */ function isMobile() {\n    if (true) return false;\n    return window.innerWidth < 768;\n}\n/**\n * Scroll to element smoothly\n */ function scrollToElement(elementId) {\n    const element = document.getElementById(elementId);\n    if (element) {\n        element.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocuments%2Fpage&page=%2Fdocuments%2Fpage&appPaths=%2Fdocuments%2Fpage&pagePath=private-next-app-dir%2Fdocuments%2Fpage.tsx&appDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csarch%5CDesktop%5CKesiTrack&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();