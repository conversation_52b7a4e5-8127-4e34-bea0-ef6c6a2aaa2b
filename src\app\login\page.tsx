'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { Card, CardHeader, CardContent, InfoCard } from '@/components/ui/Card'
import { FormField, Label, Input, Select } from '@/components/ui/Form'

export default function LoginPage() {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    role: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const roles = [
    { value: 'magistrate', label: 'Magistrate/Judicial Officer' },
    { value: 'clerk', label: 'Court Clerk' },
    { value: 'advocate', label: 'Advocate/Lawyer' }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    // Simple validation
    if (!formData.username || !formData.password || !formData.role) {
      setError('Please fill in all fields')
      setLoading(false)
      return
    }

    // Simulate login process
    try {
      await new Promise(resolve => setTimeout(resolve, 1500)) // Simulate API call
      
      // For demo purposes, accept any credentials
      localStorage.setItem('kesitrack_user', JSON.stringify({
        username: formData.username,
        role: formData.role,
        loginTime: new Date().toISOString()
      }))
      
      // Redirect to dashboard
      window.location.href = '/'
    } catch (err) {
      setError('Login failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="mx-auto w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l-3-9m3 9l3-9" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            KesiTrack
          </h1>
          <p className="text-lg text-gray-600">
            Legal Case Management System
          </p>
          <p className="text-base text-gray-500 mt-2">
            Republic of Kenya - Judiciary
          </p>
        </div>

        {/* Login Form */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold text-gray-900 text-center">
              Sign In to Your Account
            </h2>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <FormField>
                <Label htmlFor="role" required>
                  Select Your Role
                </Label>
                <Select
                  id="role"
                  value={formData.role}
                  onChange={(e) => handleInputChange('role', e.target.value)}
                  options={roles}
                  placeholder="Choose your role..."
                  error={error && !formData.role ? 'Please select your role' : ''}
                />
              </FormField>

              <FormField>
                <Label htmlFor="username" required>
                  Username or Employee ID
                </Label>
                <Input
                  id="username"
                  type="text"
                  value={formData.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  placeholder="Enter your username or employee ID"
                  error={error && !formData.username ? 'Please enter your username' : ''}
                  helpText="Use your official court system username or employee ID"
                />
              </FormField>

              <FormField>
                <Label htmlFor="password" required>
                  Password
                </Label>
                <Input
                  id="password"
                  type="password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  placeholder="Enter your password"
                  error={error && !formData.password ? 'Please enter your password' : ''}
                />
              </FormField>

              {error && (
                <div className="text-red-600 text-sm bg-red-50 border border-red-200 rounded-lg p-3">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {error}
                  </div>
                </div>
              )}

              <Button
                type="submit"
                variant="primary"
                size="large"
                loading={loading}
                className="w-full"
              >
                {loading ? 'Signing In...' : 'Sign In'}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Demo Information */}
        <InfoCard type="info" title="Demo Information">
          <p className="mb-2">
            This is a demonstration version of KesiTrack. You can log in with any credentials.
          </p>
          <p className="text-sm">
            <strong>For testing:</strong> Use any username/password combination and select your role.
          </p>
        </InfoCard>

        {/* Help Information */}
        <div className="text-center text-sm text-gray-500">
          <p>
            Need help? Contact your IT administrator or call the help desk.
          </p>
          <p className="mt-2">
            System designed for easy use by judicial officers.
          </p>
        </div>
      </div>
    </div>
  )
}
