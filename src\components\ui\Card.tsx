import React from 'react'
import { cn } from '@/lib/utils'

interface CardProps {
  children: React.ReactNode
  className?: string
  padding?: 'none' | 'small' | 'medium' | 'large'
}

interface CardHeaderProps {
  children: React.ReactNode
  className?: string
  actions?: React.ReactNode
}

interface CardContentProps {
  children: React.ReactNode
  className?: string
}

interface CardFooterProps {
  children: React.ReactNode
  className?: string
}

/**
 * Card component for organizing content in a clean, accessible way
 * Designed for easy scanning by judicial officers
 */
export function Card({ children, className, padding = 'medium' }: CardProps) {
  const paddingClasses = {
    none: 'p-0',
    small: 'p-4',
    medium: 'p-6',
    large: 'p-8',
  }
  
  return (
    <div className={cn('card', paddingClasses[padding], className)}>
      {children}
    </div>
  )
}

/**
 * Card header with optional actions
 */
export function CardHeader({ children, className, actions }: CardHeaderProps) {
  return (
    <div className={cn('card-header', className)}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          {children}
        </div>
        {actions && (
          <div className="flex items-center gap-2 ml-4">
            {actions}
          </div>
        )}
      </div>
    </div>
  )
}

/**
 * Card content area
 */
export function CardContent({ children, className }: CardContentProps) {
  return (
    <div className={cn('space-y-4', className)}>
      {children}
    </div>
  )
}

/**
 * Card footer for actions or additional info
 */
export function CardFooter({ children, className }: CardFooterProps) {
  return (
    <div className={cn('pt-4 mt-6 border-t border-gray-200', className)}>
      {children}
    </div>
  )
}

/**
 * Status card for displaying case status, urgency, etc.
 */
interface StatusCardProps {
  title: string
  status: string
  description?: string
  statusColor?: string
  icon?: React.ReactNode
  actions?: React.ReactNode
  className?: string
}

export function StatusCard({
  title,
  status,
  description,
  statusColor = 'status-pending',
  icon,
  actions,
  className
}: StatusCardProps) {
  return (
    <Card className={cn('hover:shadow-lg transition-shadow duration-200', className)}>
      <CardContent>
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            {icon && (
              <div className="flex-shrink-0 mt-1">
                {icon}
              </div>
            )}
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {title}
              </h3>
              <div className="mb-2">
                <span className={cn('status-badge', statusColor)}>
                  {status}
                </span>
              </div>
              {description && (
                <p className="text-gray-600 text-base">
                  {description}
                </p>
              )}
            </div>
          </div>
          {actions && (
            <div className="flex-shrink-0 ml-4">
              {actions}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Quick action card for common tasks
 */
interface QuickActionCardProps {
  title: string
  description: string
  icon: React.ReactNode
  onClick: () => void
  className?: string
  disabled?: boolean
}

export function QuickActionCard({
  title,
  description,
  icon,
  onClick,
  className,
  disabled = false
}: QuickActionCardProps) {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        'card text-left w-full hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed',
        'hover:scale-105 active:scale-95',
        className
      )}
    >
      <div className="flex items-center space-x-4">
        <div className="flex-shrink-0 p-3 bg-blue-50 rounded-lg">
          {icon}
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {title}
          </h3>
          <p className="text-gray-600 text-base">
            {description}
          </p>
        </div>
        <div className="flex-shrink-0">
          <svg
            className="w-6 h-6 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </div>
      </div>
    </button>
  )
}

/**
 * Info card for displaying important information
 */
interface InfoCardProps {
  type?: 'info' | 'warning' | 'success' | 'error'
  title?: string
  children: React.ReactNode
  className?: string
}

export function InfoCard({ type = 'info', title, children, className }: InfoCardProps) {
  const typeClasses = {
    info: 'bg-blue-50 border-blue-200 text-blue-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    success: 'bg-green-50 border-green-200 text-green-800',
    error: 'bg-red-50 border-red-200 text-red-800',
  }
  
  const iconClasses = {
    info: 'text-blue-500',
    warning: 'text-yellow-500',
    success: 'text-green-500',
    error: 'text-red-500',
  }
  
  const icons = {
    info: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
    warning: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    ),
    success: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
    error: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
  }
  
  return (
    <div className={cn('rounded-lg border-2 p-4', typeClasses[type], className)}>
      <div className="flex items-start space-x-3">
        <div className={cn('flex-shrink-0', iconClasses[type])}>
          {icons[type]}
        </div>
        <div className="flex-1">
          {title && (
            <h4 className="text-lg font-semibold mb-2">
              {title}
            </h4>
          )}
          <div className="text-base">
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}
